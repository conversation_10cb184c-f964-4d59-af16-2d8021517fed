import { migrate } from 'drizzle-orm/vercel-postgres/migrator';
import 'dotenv/config';

async function runMigrate() {

    // Helper function to get database instance
    const { db } = (process.env.NEXT_PUBLIC_ENV === 'local') ?
        require('./drizzle.local') :
        require('./drizzle')
    ;

    console.log("Running migrations...");

    const start = Date.now();
    await migrate(db, { migrationsFolder: "db/migrations" });
    const end = Date.now();

    console.log(`✅ Migrations completed in ${end - start}ms`);

    process.exit(0);
}

runMigrate().catch((err) => {
    console.error("❌ Migration failed");
    console.error(err);
    process.exit(1);
});
