ALTER TABLE "prompts" RENAME COLUMN "integration_id" TO "agent_integration_id";--> statement-breakpoint
ALTER TABLE "agent_ctas" DROP CONSTRAINT "agent_ctas_agent_id_foreign";
--> statement-breakpoint
ALTER TABLE "agent_frontends" DROP CONSTRAINT "agent_frontends_agent_id_foreign";
--> statement-breakpoint
ALTER TABLE "agent_integration_messages" DROP CONSTRAINT "agent_integration_messages_integration_id_foreign";
--> statement-breakpoint
ALTER TABLE "agent_integrations" DROP CONSTRAINT "agent_integrations_agent_id_foreign";
--> statement-breakpoint
ALTER TABLE "agent_integrations" DROP CONSTRAINT "agent_integrations_platform_id_foreign";
--> statement-breakpoint
ALTER TABLE "agent_models" DROP CONSTRAINT "agent_models_provider_id_foreign";
--> statement-breakpoint
ALTER TABLE "agent_models" DROP CONSTRAINT "agent_models_fallback_model_id_foreign";
--> statement-breakpoint
<PERSON>TER TABLE "agent_questions" DROP CONSTRAINT "agent_questions_agent_id_foreign";
--> statement-breakpoint
ALTER TABLE "agent_tokens" DROP CONSTRAINT "agent_tokens_agent_id_foreign";
--> statement-breakpoint
ALTER TABLE "agents" DROP CONSTRAINT "agents_model_id_foreign";
--> statement-breakpoint
ALTER TABLE "agents" DROP CONSTRAINT "agents_team_id_foreign";
--> statement-breakpoint
ALTER TABLE "prompts" DROP CONSTRAINT "prompts_agent_id_foreign";
--> statement-breakpoint
ALTER TABLE "prompts" DROP CONSTRAINT "prompts_config_id_foreign";
--> statement-breakpoint
ALTER TABLE "prompts" DROP CONSTRAINT "prompts_session_id_foreign";
--> statement-breakpoint
ALTER TABLE "prompts" DROP CONSTRAINT "prompts_agent_token_id_foreign";
--> statement-breakpoint
ALTER TABLE "prompts" DROP CONSTRAINT "prompts_integration_id_foreign";
--> statement-breakpoint
ALTER TABLE "agent_questions" ADD CONSTRAINT "agent_questions_agent_id_agents_id_fk" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "agent_ctas_agent_id_idx" ON "agent_ctas" USING btree ("agent_id");--> statement-breakpoint
CREATE INDEX "agent_frontends_agent_id_idx" ON "agent_frontends" USING btree ("agent_id");--> statement-breakpoint
CREATE INDEX "agent_integration_messages_integration_id_idx" ON "agent_integration_messages" USING btree ("integration_id");--> statement-breakpoint
CREATE INDEX "agent_integrations_agent_id_idx" ON "agent_integrations" USING btree ("agent_id");--> statement-breakpoint
CREATE INDEX "agent_integrations_platform_id_idx" ON "agent_integrations" USING btree ("platform_id");--> statement-breakpoint
CREATE INDEX "agent_models_provider_id_idx" ON "agent_models" USING btree ("provider_id");--> statement-breakpoint
CREATE INDEX "agent_questions_agent_id_idx" ON "agent_questions" USING btree ("agent_id");--> statement-breakpoint
CREATE INDEX "agent_tokens_agent_id_idx" ON "agent_tokens" USING btree ("agent_id");--> statement-breakpoint
CREATE INDEX "agents_model_id_idx" ON "agents" USING btree ("model_id");--> statement-breakpoint
CREATE INDEX "agents_team_id_idx" ON "agents" USING btree ("team_id");--> statement-breakpoint
CREATE INDEX "prompts_agent_id_idx" ON "prompts" USING btree ("agent_id");--> statement-breakpoint
CREATE INDEX "prompts_config_id_idx" ON "prompts" USING btree ("config_id");--> statement-breakpoint
CREATE INDEX "prompts_session_id_idx" ON "prompts" USING btree ("session_id");--> statement-breakpoint
CREATE INDEX "prompts_token_id_idx" ON "prompts" USING btree ("agent_token_id");--> statement-breakpoint
CREATE INDEX "prompts_integration_id_idx" ON "prompts" USING btree ("agent_integration_id");