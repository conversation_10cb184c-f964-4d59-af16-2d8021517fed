CREATE TABLE "agent_frontends" (
	"id" bigint PRIMARY KEY NOT NULL,
	"agent_id" bigint,
	"theme" "theme_enum" DEFAULT 'dark' NOT NULL,
	"primary_color" char(25) NOT NULL,
	"image_path" varchar(250),
	"background_color" char(25),
	"background_path" varchar(250),
	"display_font_url" varchar(250),
	"display_font_name" varchar(100),
	"body_font_url" varchar(250),
	"body_font_name" varchar(100),
	"favicon_path" varchar(100),
	"meta_title" jsonb,
	"meta_description" jsonb,
	"meta_keywords" jsonb,
	"creator_name" jsonb,
	"creator_description" jsonb,
	"creator_url" jsonb,
	"app_icon_path" varchar(100),
	"app_icon_color" char(25),
	"footer_text" jsonb,
	"hide_footer_cta" boolean DEFAULT false NOT NULL,
	"footer_cta_label" jsonb,
	"footer_cta_url" jsonb,
	"embed_hide_header" boolean DEFAULT false NOT NULL,
	"embed_icon_color" varchar(25),
	"embed_icon_path" varchar(100),
	"show_media" boolean DEFAULT false,
	"media_collections" jsonb,
	"intro_preamble" jsonb,
	"intro_headline" jsonb,
	"intro_description" jsonb,
	"questions_title" jsonb,
	"custom_styles" varchar(1000),
	"custom_scripts" varchar(1000),
	"has_basic_auth" boolean DEFAULT false,
	"basic_auth_user" varchar(50),
	"basic_auth_password" varchar(500),
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
ALTER TABLE "agent_frontends" ADD CONSTRAINT "agent_frontends_agent_id_foreign" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE no action ON UPDATE no action;