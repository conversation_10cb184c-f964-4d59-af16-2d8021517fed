CREATE TYPE "public"."verbosity_enum" AS ENUM('low', 'medium', 'high');--> statement-breakpoint
ALTER TYPE "public"."reasoning_effort_enum" ADD VALUE 'minimal' BEFORE 'low';--> statement-breakpoint
ALTER TABLE "agent_models" ADD COLUMN "supports_verbosity" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_models" ADD COLUMN "supports_temperature" boolean DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE "agents" ADD COLUMN "model_verbosity" "verbosity_enum";