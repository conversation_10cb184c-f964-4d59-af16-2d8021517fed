CREATE TABLE "agent_integration_messages" (
	"id" bigint PRIMARY KEY NOT NULL,
	"message" varchar(10000) NOT NULL,
    "language" varchar(10) NOT NULL,
	"integration_id" bigint NOT NULL,
	"conversation_id" varchar(100) NOT NULL,
	"user_id" varchar(100),
	"prompted_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
ALTER TYPE client_enum RENAME TO client_enum_old;
CREATE TYPE client_enum AS ENUM('standalone', 'embedded', 'api', 'integration');--> statement-breakpoint
ALTER TABLE prompts ALTER COLUMN client DROP DEFAULT;
ALTER TABLE prompts ALTER COLUMN client TYPE client_enum USING client::text::client_enum;
ALTER TABLE prompts ALTER COLUMN client SET DEFAULT 'standalone'::client_enum;
DROP TYPE client_enum_old;--> statement-breakpoint
