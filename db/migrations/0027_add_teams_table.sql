CREATE TABLE "teams" (
	"id" bigint NOT NULL,
	"name" varchar(255) NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"is_trial" boolean DEFAULT false NOT NULL,
	"has_custom_corpus" boolean DEFAULT false,
	"corpus_api_key" varchar(500),
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "has_custom_corpus";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "corpus_api_key";