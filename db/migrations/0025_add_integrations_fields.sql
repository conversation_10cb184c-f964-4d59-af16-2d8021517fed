ALTER TABLE "agent_integrations" ALTER COLUMN "account" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "key" varchar(25);--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "description" varchar(500);--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "has_account" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "has_secret" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "has_cue" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "has_welcome" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "has_cta" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "can_auto_initialize" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "param_1_label" varchar(50);--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "param_2_label" varchar(50);--> statement-breakpoint
ALTER TABLE "agent_integration_platforms" ADD COLUMN "param_3_label" varchar(50);--> statement-breakpoint
ALTER TABLE "agent_integrations" ADD COLUMN "auto_initialize" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_integrations" ADD COLUMN "param_1" varchar(500);--> statement-breakpoint
ALTER TABLE "agent_integrations" ADD COLUMN "param_2" varchar(500);--> statement-breakpoint
ALTER TABLE "agent_integrations" ADD COLUMN "param_3" varchar(500);