ALTER TYPE "public"."client_enum" ADD VALUE 'integration';--> statement-breakpoint
CREATE TABLE "agent_integration_platforms" (
	"id" bigint PRIMARY KEY NOT NULL,
	"name" varchar(50) NOT NULL,
	"is_active" boolean DEFAULT false NOT NULL,
	"image_path" varchar(250),
	"endpoint_url" varchar(250),
	"is_public" boolean DEFAULT false NOT NULL,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "agent_integrations" (
	"id" bigint PRIMARY KEY NOT NULL,
	"name" varchar(50) NOT NULL,
    "agent_id" bigint,
	"platform_id" bigint,
	"is_active" boolean DEFAULT false NOT NULL,
	"account" varchar(250) NOT NULL,
	"secret" varchar(250),
	"token" varchar(500),
	"cue" jsonb,
	"welcome" jsonb,
	"cta" jsonb,
	"languages" jsonb,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
ALTER TABLE "prompts" ADD COLUMN "integration_id" bigint;
