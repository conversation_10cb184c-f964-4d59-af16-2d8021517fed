ALTER TABLE "sources" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
DROP TABLE "sources" CASCADE;--> statement-breakpoint
ALTER TABLE "agent_cta_clicks" ALTER COLUMN "cta_id" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "agent_ctas" ADD CONSTRAINT "agent_ctas_agent_id_foreign" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "agent_integration_messages" ADD CONSTRAINT "agent_integration_messages_integration_id_foreign" FOREIGN KEY ("integration_id") REFERENCES "public"."agent_integrations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "agent_integrations" ADD CONSTRAINT "agent_integrations_agent_id_foreign" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "agent_integrations" ADD CONSTRAINT "agent_integrations_platform_id_foreign" FOREIGN KEY ("platform_id") REFERENCES "public"."agent_integration_platforms"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "agent_questions" ADD CONSTRAINT "agent_questions_agent_id_foreign" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "agent_tokens" ADD CONSTRAINT "agent_tokens_agent_id_foreign" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "configs" ADD CONSTRAINT "configs_agent_id_foreign" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prompt_sources" ADD CONSTRAINT "prompt_sources_prompt_id_foreign" FOREIGN KEY ("prompt_id") REFERENCES "public"."prompts"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prompts" ADD CONSTRAINT "prompts_agent_id_foreign" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prompts" ADD CONSTRAINT "prompts_config_id_foreign" FOREIGN KEY ("config_id") REFERENCES "public"."configs"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prompts" ADD CONSTRAINT "prompts_session_id_foreign" FOREIGN KEY ("session_id") REFERENCES "public"."sessions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prompts" ADD CONSTRAINT "prompts_agent_token_id_foreign" FOREIGN KEY ("agent_token_id") REFERENCES "public"."agent_tokens"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prompts" ADD CONSTRAINT "prompts_integration_id_foreign" FOREIGN KEY ("integration_id") REFERENCES "public"."agent_integrations"("id") ON DELETE no action ON UPDATE no action;