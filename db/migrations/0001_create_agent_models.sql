CREATE TABLE "agent_model_providers" (
	"id" bigint NOT NULL,
	"name" varchar(50) NOT NULL,
	"key" varchar(50) NOT NULL,
	"is_active" boolean DEFAULT false NOT NULL,
	"synced_at" timestamp(6) NOT NULL DEFAULT (now() AT TIME ZONE 'utc'::text)
);
--> statement-breakpoint
CREATE TABLE "agent_models" (
	"id" bigint NOT NULL,
	"name" varchar(50) NOT NULL,
	"key" varchar(50) NOT NULL,
	"provider_id" bigint,
	"provider_model" varchar(50) NOT NULL,
	"type" varchar(255) NOT NULL,
	"is_recommended" boolean DEFAULT false NOT NULL,
	"max_tokens" integer DEFAULT 4096 NOT NULL,
	"stop_sequence" varchar(50),
	"strip_sequence" varchar(50),
	"languages" jsonb,
	"input_cost" numeric(20, 10),
	"output_cost" numeric(20, 10),
	"num_credits" smallint NOT NULL,
	"seq" smallint DEFAULT '0' NOT NULL,
	"is_active" boolean DEFAULT false NOT NULL,
	"synced_at" timestamp(6) NOT NULL DEFAULT (now() AT TIME ZONE 'utc'::text),
	CONSTRAINT "agent_models_type_check" CHECK ((type)::text = ANY ((ARRAY['limited'::character varying, 'standard'::character varying, 'premium'::character varying, 'reasoning'::character varying, 'admin'::character varying])::text[]))
);
--> statement-breakpoint

ALTER TABLE "agents" DROP COLUMN "model_id";--> statement-breakpoint
ALTER TABLE "agents" ADD COLUMN "model_id" bigint;--> statement-breakpoint

CREATE UNIQUE INDEX "idx_agent_model_providers_id" ON "agent_model_providers" USING btree (
  "id" "int8_ops" ASC NULLS LAST
);
ALTER TABLE "agent_models" ADD CONSTRAINT "agent_models_provider_id_foreign" FOREIGN KEY ("provider_id") REFERENCES "public"."agent_model_providers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint

CREATE UNIQUE INDEX "idx_agent_models_id" ON "agent_models" USING btree (
  "id" "int8_ops" ASC NULLS LAST
);
ALTER TABLE "agents" ADD CONSTRAINT "agents_model_id_foreign" FOREIGN KEY ("model_id") REFERENCES "public"."agent_models"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint

ALTER TABLE "agents" ALTER COLUMN "id" TYPE bigint;
ALTER TABLE "agents" DROP COLUMN "model_stop_sequence";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "model_strip_sequence";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "model_provider";

ALTER TABLE "configs" RENAME COLUMN "model_id" TO "model_key";
ALTER TABLE "configs" ADD COLUMN "model_id" bigint;
ALTER TABLE "configs" ADD CONSTRAINT "configs_model_id_foreign" FOREIGN KEY ("model_id") REFERENCES "public"."agent_models"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
UPDATE
    configs
SET
    model_id = (SELECT id FROM agent_models WHERE provider_model = configs.model_key)
WHERE
    model_key IS NOT NULL
;

CREATE INDEX "idx_agent_models_key" ON "agent_models" USING btree (
  "key" "varchar_ops" ASC NULLS LAST
);
