CREATE SEQUENCE "public"."searches_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE TABLE "searches" (
	"id" bigint DEFAULT nextval('searches_id_seq'::regclass) NOT NULL,
	"query" text NOT NULL,
	"filters" jsonb,
	"results" jsonb,
	"prompt_id" bigint,
	"agent_id" bigint NOT NULL,
	"agent_token_id" bigint,
	"searched_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
CREATE INDEX "searches_prompt_id_idx" ON "searches" USING btree ("prompt_id");--> statement-breakpoint
CREATE INDEX "searches_agent_id_idx" ON "searches" USING btree ("agent_id");
