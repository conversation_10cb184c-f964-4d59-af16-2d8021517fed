CREATE SEQUENCE "public"."shares_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START WITH 1 CACHE 1;--> statement-breakpoint
CREATE TABLE "shares" (
	"id" bigint DEFAULT nextval('shares_id_seq'::regclass) NOT NULL,
	"shared_prompt_id" bigint NOT NULL,
	"session_id" varchar(100),
	"conversation_id" varchar(100),
	"user_id" varchar(100),
	"agent_id" integer,
	"shared_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
