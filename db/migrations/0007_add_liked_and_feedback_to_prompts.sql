UPDATE "prompts" SET "flagged" = false;--> statement-breakpoint
ALTER TABLE "prompts" ALTER COLUMN "flagged" SET DEFAULT false;--> statement-breakpoint
ALTER TABLE "prompts" ALTER COLUMN "flagged" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "prompts" ADD COLUMN "liked" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "prompts" ADD COLUMN "feedback" text;--> statement-breakpoint
CREATE INDEX "prompts_liked_idx" ON "prompts" USING btree ("liked");--> statement-breakpoint
CREATE INDEX "prompts_flagged_idx" ON "prompts" USING btree ("flagged");
