ALTER TABLE "agents" ALTER COLUMN "model_temperature" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "agents" ALTER COLUMN "model_top_p" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "agents" ALTER COLUMN "model_frequency_penalty" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "agents" ALTER COLUMN "model_presence_penalty" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "agents" ADD CONSTRAINT "agents_team_id_foreign" FOREIGN KEY ("team_id") REFERENCES "public"."teams"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "color";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "image_path";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "intro_preamble";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "intro_headline";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "intro_description";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "theme";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "response_footer_content";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "response_footer_threshold";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "background_path";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "background_color";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "description";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "creator_url";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "creator_name";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "custom_styles";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "hide_header";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "footer_text";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "questions_title";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "hide_footer_cta";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "footer_cta_label";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "footer_cta_url";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "meta_title";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "meta_description";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "meta_keywords";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "custom_scripts";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "favicon_path";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "icon_path";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "icon_color";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "display_font_url";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "body_font_url";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "beacon_icon_path";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "show_media";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "media_collections";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "has_basic_auth";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "basic_auth_user";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "basic_auth_password";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "display_font_name";--> statement-breakpoint
ALTER TABLE "agents" DROP COLUMN "body_font_name";