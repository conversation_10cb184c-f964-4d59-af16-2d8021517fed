CREATE TYPE "public"."display_mode" AS ENUM('footer', 'modal');--> statement-breakpoint
CREATE TYPE "public"."response_basis" AS ENUM('conversation_id', 'session_id', 'device_id', 'user_id');--> statement-breakpoint
CREATE TYPE "public"."timing_mode" AS ENUM('threshold', 'always', 'once', 'interval');--> statement-breakpoint
CREATE TABLE "agent_ctas" (
	"id" bigint PRIMARY KEY NOT NULL,
	"name" varchar(100) NOT NULL,
	"agent_id" integer NOT NULL,
	"is_active" boolean DEFAULT false,
	"standalone_active" boolean DEFAULT false,
	"embedded_active" boolean DEFAULT false,
	"api_active" boolean DEFAULT false,
	"display_mode" "display_mode" DEFAULT 'footer',
	"content" jsonb,
	"timing_mode" "timing_mode" NOT NULL,
	"response_basis" "response_basis" NOT NULL,
	"response_number" integer DEFAULT 0,
	"priority" integer DEFAULT 0,
	"integrations" jsonb,
	"synced_at" timestamp(6) DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL
);
--> statement-breakpoint
ALTER TABLE "agent_cta_clicks" ADD COLUMN "cta_id" bigint;
