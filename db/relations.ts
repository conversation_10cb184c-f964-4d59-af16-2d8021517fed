import { relations } from "drizzle-orm/relations";
import { agentModels, agents, agentModelProviders } from "./schema";

export const agentsRelations = relations(agents, ({one}) => ({
	agentModel: one(agentModels, {
		fields: [agents.modelId],
		references: [agentModels.id]
	}),
}));

export const agentModelsRelations = relations(agentModels, ({one, many}) => ({
	agents: many(agents),
	agentModelProvider: one(agentModelProviders, {
		fields: [agentModels.providerId],
		references: [agentModelProviders.id]
	}),
}));

export const agentModelProvidersRelations = relations(agentModelProviders, ({many}) => ({
	agentModels: many(agentModels),
}));