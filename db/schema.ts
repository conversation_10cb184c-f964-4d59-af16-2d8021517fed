import { pgTable, index, bigint, jsonb, integer, boolean, timestamp, varchar, cidr, char, real, text, doublePrecision, smallint, check, foreignKey, numeric, pgSequence, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const clientEnum = pgEnum("client_enum", ["standalone", "embedded", "api", "integration"]);
export const frontendEnum = pgEnum("frontend_enum", ['agent', 'social']);
export const modelEnum = pgEnum("model_enum", ['collection', 'contributor', 'organization', 'source']);
export const themeEnum = pgEnum("theme_enum", ['dark', 'light']);
export const reasoningEffortEnum = pgEnum("reasoning_effort_enum", ['minimal', 'low', 'medium', 'high']);
export const verbosityEnum = pgEnum("verbosity_enum", ['low', 'medium', 'high']);
export const apiResponseFormatEnum = pgEnum("api_response_format", ['raw', 'text', 'html', 'json']);
export const ctaDisplayModeEnum = pgEnum("display_mode", ['footer', 'modal']);
export const ctaTimingModeEnum = pgEnum("timing_mode", ['threshold', 'always', 'once', 'interval']);
export const ctaResponseBasisEnum = pgEnum("response_basis", ['conversation_id', 'session_id', 'device_id', 'user_id']);

export const configsIdSeq = pgSequence("configs_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "2147483647", cache: "1", cycle: false });
export const evaluationsIdSeq = pgSequence("evaluations_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });
export const itemImpressionsIdSeq = pgSequence("item_impressions_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });
export const itemReferralsIdSeq = pgSequence("item_referrals_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });
export const itemViewsIdSeq = pgSequence("item_views_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });
export const promptSourcesPromptIdSeq = pgSequence("prompt_sources_prompt_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });
export const promptsIdSeq = pgSequence("prompts_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });
export const sharesIdSeq = pgSequence("shares_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });
export const agentCtaClicksIdSeq = pgSequence("agent_cta_clicks_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });
export const agentIntegrationMessagesIdSeq = pgSequence("agent_integration_messages_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });
export const searchesIdSeq = pgSequence("searches_id_seq", {  startWith: "1", increment: "1", minValue: "1", maxValue: "9223372036854775807", cache: "1", cycle: false });

export const agent_questions = pgTable("agent_questions", {
	id: bigint({ mode: "number" }).notNull(),
	question: jsonb(),
	agent_id: integer("agent_id").notNull().references(() => agents.id, { onDelete: 'cascade' }),
	seq: integer().default(0).notNull(),
	is_active: boolean("is_active").default(false),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
}, (table) => [
	index("agent_questions_agent_id_idx").on(table.agent_id),
]);

export const sessions = pgTable("sessions", {
	id: varchar({ length: 100 }).notNull(),
	language: varchar({ length: 10 }).notNull(),
	started_at: timestamp("started_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
	user_agent: varchar("user_agent", { length: 500 }),
	host: varchar({ length: 100 }).notNull(),
	ip_country: char("ip_country", { length: 2 }),
	ip_region: varchar("ip_region", { length: 100 }),
	ip_city: varchar("ip_city", { length: 100 }),
	ip_latitude: real("ip_latitude"),
	ip_longitude: real("ip_longitude"),
	ip_timezone: varchar("ip_timezone", { length: 1000 }),
	parent_url: varchar("parent_url", { length: 1000 }),
	parent_host: varchar("parent_host", { length: 100 }),
	user_id: varchar("user_id", { length: 100 }),
});

export const prompt_sources = pgTable("prompt_sources", {
	prompt_id: bigint("prompt_id", { mode: "number" }).default(sql`nextval('prompt_sources_prompt_id_seq'::regclass)`).notNull(),
	source_id: bigint("source_id", { mode: "number" }).notNull(),
	snippet: text().notNull(),
	score: doublePrecision(),
}, (table) => [
	foreignKey({
		columns: [table.prompt_id],
		foreignColumns: [prompts.id],
		name: "prompt_sources_prompt_id_foreign"
	}),
]);

export const item_impressions = pgTable("item_impressions", {
	id: bigint({ mode: "number" }).default(sql`nextval('item_impressions_id_seq'::regclass)`).notNull(),
	frontend: frontendEnum().notNull(),
	item_model: modelEnum("item_model").notNull(),
	item_id: bigint("item_id", { mode: "number" }).notNull(),
	prompt_id: bigint("prompt_id", { mode: "number" }),
	user_id: bigint("user_id", { mode: "number" }),
	viewed_at: timestamp("viewed_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
});

export const configs = pgTable("configs", {
	id: integer().default(sql`nextval('configs_id_seq'::regclass)`).notNull(),
	system_prompt: text("system_prompt"),
	max_tokens: integer("max_tokens"),
	temperature: doublePrecision(),
	top_p: doublePrecision("top_p"),
	model_key: char("model_key", { length: 128 }),
	model_id: bigint({ mode: "number" }),
	created_at: timestamp("created_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
	agent_id: integer("agent_id"),
	frequency_penalty: doublePrecision("frequency_penalty").default(0),
	presence_penalty: doublePrecision("presence_penalty").default(0),
	reasoning_effort: reasoningEffortEnum(),
	verbosity: verbosityEnum(),
}, (table) => [
	index("configs_agent_id_idx").on(table.agent_id),
]);

export const item_referrals = pgTable("item_referrals", {
	id: bigint({ mode: "number" }).default(sql`nextval('item_referrals_id_seq'::regclass)`).notNull(),
	frontend: frontendEnum().notNull(),
	item_model: modelEnum("item_model").notNull(),
	item_id: bigint("item_id", { mode: "number" }).notNull(),
	prompt_id: bigint("prompt_id", { mode: "number" }),
	user_id: bigint("user_id", { mode: "number" }),
	referred_at: timestamp("referred_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
});

export const agent_tokens = pgTable("agent_tokens", {
	id: bigint({ mode: "number" }).notNull(),
	name: varchar({ length: 100 }).notNull(),
	token: varchar({ length: 500 }),
	agent_id: integer("agent_id").notNull(),
	is_active: boolean("is_active").default(false),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
}, (table) => [
	index("agent_tokens_agent_id_idx").on(table.agent_id),
]);

export const evaluations = pgTable("evaluations", {
	id: bigint({ mode: "number" }).default(sql`nextval('evaluations_id_seq'::regclass)`).notNull(),
	promptId: integer("prompt_id"),
	expected_response: text("expected_response"),
	exp_res_at: timestamp("exp_res_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`),
	parent_id: integer("parent_id"),
	flagged: boolean().default(false).notNull(),
	accuracy_score: smallint("accuracy_score"),
	accuracy_reason: text("accuracy_reason"),
	sympathy_score: smallint("sympathy_score"),
	sympathy_reason: text("sympathy_reason"),
	eval_model: text("eval_model"),
	hide: boolean().default(false).notNull(),
});

export const item_views = pgTable("item_views", {
	id: bigint({ mode: "number" }).default(sql`nextval('item_views_id_seq'::regclass)`).notNull(),
	frontend: frontendEnum().notNull(),
	item_model: modelEnum("item_model").notNull(),
	item_id: bigint("item_id", { mode: "number" }).notNull(),
	prompt_id: bigint("prompt_id", { mode: "number" }),
	user_id: bigint("user_id", { mode: "number" }),
	viewed_at: timestamp("viewed_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
});

export const categories = pgTable("categories", {
	id: bigint({ mode: "number" }).notNull(),
	name: varchar({ length: 255 }).notNull(),
	parent_id: bigint("parent_id", { mode: "number" }),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
	slug: varchar({ length: 128 }),
	tagline: varchar({ length: 256 }),
	description: text(),
	is_active: boolean("is_active").default(false),
});

export const agents = pgTable("agents", {

	// ID
	id: bigint({ mode: "number" }).notNull(),
	name: varchar({ length: 64 }).notNull(),

	// Domain
	slug: varchar({ length: 64 }),
	root_domain: varchar("root_domain", { length: 50 }),
	vanity_domain: varchar("vanity_domain", { length: 64 }),

	// Options
	has_semantic_search: boolean("has_semantic_search").default(false),
	is_nonbillable: boolean("is_nonbillable").default(false),
	max_memories: smallint("max_memories"),

	// Model
	model_id: bigint("model_id", { mode: "number" }),
	model_system_prompt: text("model_system_prompt"),
	model_context_prompt: varchar("model_context_prompt", { length: 1000 }),
	model_max_tokens: integer("model_max_tokens").notNull(),
	model_temperature: doublePrecision("model_temperature"),
	model_top_p: doublePrecision("model_top_p"),
	model_frequency_penalty: doublePrecision("model_frequency_penalty").default(0),
	model_presence_penalty: doublePrecision("model_presence_penalty").default(0),
	model_reasoning_effort: reasoningEffortEnum(),
	model_verbosity: verbosityEnum(),

	// Persona
	persona_name: jsonb("persona_name"),
	persona_tagline: jsonb("persona_tagline"),
	persona_description: jsonb("persona_description"),
	persona_avatar_path: varchar("persona_avatar_path", { length: 128 }),

	// Status
	is_active: boolean("is_active").default(false),
	is_locked: boolean("is_locked").default(false),
	is_open_source: boolean("is_open_source").default(false),
	is_selectable: boolean("is_selectable").default(false),
	is_extensible: boolean("is_extensible").default(false),
	is_template: boolean("is_template").default(false),
	is_approved: boolean("is_approved").default(false),
	parent_id: integer("parent_id"),

	// Marketplace
	marketplace_active: boolean("marketplace_active").default(false),
	target_worldview: jsonb("target_worldview"),

	// Languages & Translations
	default_language: varchar("default_language", { length: 5 }),
	default_translation: varchar("default_translation", { length: 5 }),
	supported_languages: jsonb("supported_languages"),
	auto_translate: boolean("auto_translate").default(false),
	auto_translate_languages: jsonb("auto_translate_languages"),
	supported_translations: jsonb("supported_translations"),

	// Corpus
	use_team_corpus: boolean("use_team_corpus").default(false),
	disable_community_corpus: boolean("disable_community_corpus").default(false),
	categories: jsonb(),
	collections: jsonb(),
	contributors: jsonb(),
	sources: jsonb(),
	classification_id: integer("classification_id"),
	tags: jsonb(),

	// API
	api_response_format: apiResponseFormatEnum(),
	lock_system_prompt: boolean("lock_system_prompt").default(false),
	strip_markdown: boolean("strip_markdown").default(false),

	// Owner
	user_id: bigint("user_id", { mode: "number" }),
	team_id: bigint("team_id", { mode: "number" }),

	// Debug & Sort
	debug: boolean("debug").default(false),
	seq: integer().default(0).notNull(),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),

}, (table) => [
	index("agents_model_id_idx").on(table.model_id),
	index("agents_team_id_idx").on(table.team_id),
]);

export const agent_frontends = pgTable("agent_frontends", {

	id: bigint({ mode: "number" }).notNull().primaryKey(),
	agent_id: bigint("agent_id", { mode: "number" }),

	// Branding
	theme: themeEnum().default('dark').notNull(),
	primary_color: varchar({ length: 25 }).notNull(),
	image_path: varchar("image_path", { length: 250 }),
	background_color: char("background_color", { length: 25 }),
	background_path: varchar("background_path", { length: 250 }),
	display_font_url: varchar("display_font_url", { length: 250 }),
	display_font_name: varchar("display_font_name", { length: 100 }),
	body_font_url: varchar("body_font_url", { length: 250 }),
	body_font_name: varchar("body_font_name", { length: 100 }),

	// Meta
	favicon_path: varchar("favicon_path", { length: 100 }),
	meta_title: jsonb("meta_title"),
	meta_description: jsonb("meta_description"),
	meta_keywords: jsonb("meta_keywords"),

	// Attribution
	creator_name: jsonb("creator_name"),
	creator_description: jsonb("creator_description"),
	creator_url: jsonb("creator_url"),

	// App
	app_icon_path: varchar("app_icon_path", { length: 100 }),
	app_icon_color: char("app_icon_color", { length: 25 }),

	// Footer
	footer_text: jsonb("footer_text"),
	hide_footer_cta: boolean("hide_footer_cta").default(false).notNull(),
	footer_cta_label: jsonb("footer_cta_label"),
	footer_cta_url: jsonb("footer_cta_url"),

	// Embed
	embed_hide_header: boolean("embed_hide_header").default(false).notNull(),
	embed_icon_color: varchar("embed_icon_color", { length: 25 }),
	embed_icon_path: varchar("embed_icon_path", { length: 100 }),

	// Media
	show_media: boolean("show_media").default(false),
	media_collections: jsonb("media_collections"),

	// Intro
	intro_preamble: jsonb("intro_preamble"),
	intro_headline: jsonb("intro_headline"),
	intro_description: jsonb("intro_description"),
	questions_title: jsonb("questions_title"),

	// Custom Code
	custom_styles: varchar("custom_styles", { length: 1000 }),
	custom_scripts: varchar("custom_scripts", { length: 1000 }),

	// Security
	has_basic_auth: boolean("has_basic_auth").default(false),
	basic_auth_user: varchar("basic_auth_user", { length: 50 }),
	basic_auth_password: varchar("basic_auth_password", { length: 500 }),

	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),

}, (table) => [
	index("agent_frontends_agent_id_idx").on(table.agent_id),
]);

export const agent_model_providers = pgTable("agent_model_providers", {
	id: bigint({ mode: "number" }).notNull(),
	name: varchar({ length: 50 }).notNull(),
	key: varchar({ length: 255 }).notNull(),
	is_active: boolean().default(false).notNull(),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
}, (table) => [
	check("agent_model_providers_key_check", sql`(key)::text = ANY ((ARRAY['anthropic'::character varying, 'fireworks'::character varying, 'groq'::character varying, 'openai'::character varying, 'together'::character varying, 'xai'::character varying])::text[])`),
]);

export const agent_models = pgTable("agent_models", {
	id: bigint({ mode: "number" }).notNull(),
	name: varchar({ length: 50 }).notNull(),
	key: varchar({ length: 50 }).notNull(),
	provider_id: bigint("provider_id", { mode: "number" }),
	provider_model: varchar("provider_model", { length: 50 }).notNull(),
	type: varchar({ length: 255 }).notNull(),
	is_recommended: boolean().default(false).notNull(),
	max_tokens: integer("max_tokens").default(4096).notNull(),
	stop_sequence: varchar("stop_sequence", { length: 50 }),
	strip_sequence: varchar("strip_sequence", { length: 50 }),
	languages: jsonb(),
	supports_reasoning_effort: boolean().default(false).notNull(),
	supports_verbosity: boolean().default(false).notNull(),
	supports_temperature: boolean().default(true).notNull(),
	supports_top_p: boolean().default(true).notNull(),
	supports_frequency_penalty: boolean().default(true).notNull(),
	supports_presence_penalty: boolean().default(true).notNull(),
	fallback_model_id: bigint("fallback_model_id", { mode: "number" }),
	input_cost: numeric("input_cost", { precision: 15, scale:  2 }),
	output_cost: numeric("output_cost", { precision: 15, scale:  2 }),
	num_credits: smallint("num_credits").notNull(),
	seq: smallint().default(sql`'0'`).notNull(),
	is_active: boolean().default(false).notNull(),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
}, (table) => [
	index("agent_models_provider_id_idx").on(table.provider_id),
	check("agent_models_type_check", sql`(type)::text = ANY ((ARRAY['limited'::character varying, 'standard'::character varying, 'premium'::character varying, 'reasoning'::character varying, 'admin'::character varying])::text[])`),
]);

export const agent_integration_platforms = pgTable("agent_integration_platforms", {
	id: bigint({ mode: "number" }).notNull().primaryKey(),
	name: varchar({ length: 50 }).notNull(),
	key: varchar({ length: 25 }),
	description: varchar({ length: 500 }),
	is_active: boolean().default(false).notNull(),
	image_path: varchar({ length: 250 }),
	endpoint_url: varchar({ length: 250 }),
	languages: jsonb(),
	has_account: boolean().default(false).notNull(),
	has_secret: boolean().default(false).notNull(),
	has_cue: boolean().default(false).notNull(),
	has_welcome: boolean().default(false).notNull(),
	has_cta: boolean().default(false).notNull(),
	can_auto_initialize: boolean().default(false).notNull(),
	param_1_label: varchar({ length: 50 }),
	param_2_label: varchar({ length: 50 }),
	param_3_label: varchar({ length: 50 }),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
});

export const agent_integrations = pgTable("agent_integrations", {
	id: bigint({ mode: "number" }).notNull().primaryKey(),
	name: varchar({ length: 50 }).notNull(),
	agent_id: bigint("agent_id", { mode: "number" }),
	platform_id: bigint("platform_id", { mode: "number" }),
	is_active: boolean().default(false).notNull(),
	account: varchar({ length: 250 }),
	secret: varchar({ length: 500 }),
	token: varchar({ length: 500 }),
	cue: jsonb(),
	welcome: jsonb(),
	cta: jsonb(),
	languages: jsonb(),
	auto_initialize: boolean().default(false).notNull(),
	param_1: varchar({ length: 500 }),
	param_2: varchar({ length: 500 }),
	param_3: varchar({ length: 500 }),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
}, (table) => [
	index("agent_integrations_agent_id_idx").on(table.agent_id),
	index("agent_integrations_platform_id_idx").on(table.platform_id),
]);

export const agent_integration_messages = pgTable("agent_integration_messages", {
	id: bigint({ mode: "number" }).default(sql`nextval('agent_integration_messages_id_seq'::regclass)`).notNull(),
	message: varchar({ length: 10000 }).notNull(),
	language: varchar({ length: 10 }).notNull(),
	integration_id: bigint("integration_id", { mode: "number" }).notNull(),
	conversation_id: varchar("conversation_id", { length: 100 }).notNull(),
	user_id: varchar("user_id", { length: 100 }),
	received_at: timestamp("prompted_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
}, (table) => [
	index("agent_integration_messages_integration_id_idx").on(table.integration_id),
]);

export const prompts = pgTable("prompts", {
	id: bigint({ mode: "number" }).default(sql`nextval('prompts_id_seq'::regclass)`).notNull(),
	language: varchar({ length: 10 }).notNull(),
	prompt: text().notNull(),
	response: text(),
	prompted_at: timestamp("prompted_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
	response_started_at: timestamp("response_started_at", { precision: 6, mode: 'string' }),
	response_completed_at: timestamp("response_completed_at", { precision: 6, mode: 'string' }),
	flagged: boolean().default(false).notNull(),
	score: smallint(),
	config_id: integer("config_id"),
	session_id: varchar("session_id", { length: 100 }),
	conversation_id: varchar("conversation_id", { length: 100 }),
	translated_prompt: text("translated_prompt"),
	translated_response: text("translated_response"),
	user_id: varchar("user_id", { length: 100 }),
	agent_id: integer("agent_id"),
	translation: varchar({ length: 4 }),
	client: clientEnum().default('standalone'),
	prompt_tokens: integer("prompt_tokens"),
	response_tokens: integer("response_tokens"),
	agent_token_id: integer("agent_token_id"),
	chat_tokens: integer("chat_tokens"),
	reasoning: text(),
	liked: boolean().default(false).notNull(),
	feedback: text(),
	agent_integration_id: bigint({ mode: "number" }),
	device_id: varchar("device_id", { length: 100 }),
	reasoning_tokens: integer("reasoning_tokens"),
	cached: boolean("cached").default(false).notNull(),
}, (table) => [
	index("prompts_liked_idx").on(table.liked),
	index("prompts_flagged_idx").on(table.flagged),
	index("prompts_agent_id_idx").on(table.agent_id),
	index("prompts_config_id_idx").on(table.config_id),
	index("prompts_session_id_idx").on(table.session_id),
	index("prompts_token_id_idx").on(table.agent_token_id),
	index("prompts_integration_id_idx").on(table.agent_integration_id),
	index("prompts_cached_idx").on(table.cached),
]);

export const shares = pgTable("shares", {
	id: bigint({ mode: "number" }).default(sql`nextval('shares_id_seq'::regclass)`).notNull(),
	shared_prompt_id: bigint("shared_prompt_id", { mode: "number" }).notNull(),
	session_id: varchar("session_id", { length: 100 }),
	conversation_id: varchar("conversation_id", { length: 100 }),
	user_id: varchar("user_id", { length: 100 }),
	agent_id: integer("agent_id"),
	shared_at: timestamp("shared_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
});

export const agent_cta_clicks = pgTable("agent_cta_clicks", {
	id: bigint({ mode: "number" }).default(sql`nextval('agent_cta_clicks_id_seq'::regclass)`).notNull(),
	cta_id: bigint("cta_id", { mode: "number" }),
	agent_id: bigint("agent_id", { mode: "number" }).notNull(),
	prompt_id: bigint("prompt_id", { mode: "number" }).notNull(),
	clicked_at: timestamp("clicked_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
});

export const teams = pgTable("teams", {
	id: bigint({ mode: "number" }).notNull().primaryKey(),
	name: varchar({ length: 255 }).notNull(),
	is_active: boolean().default(true).notNull(),
	is_trial: boolean().default(false).notNull(),
	has_custom_corpus: boolean("has_custom_corpus").default(false),
	corpus_api_key: varchar("corpus_api_key", { length: 500 }),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
});

export const agent_ctas = pgTable("agent_ctas", {
	id: bigint({ mode: "number" }).notNull().primaryKey(),
	name: varchar({ length: 100 }).notNull(),
	agent_id: integer("agent_id").notNull(),
	is_active: boolean("is_active").default(false),
	standalone_active: boolean("standalone_active").default(false),
	embedded_active: boolean("embedded_active").default(false),
	api_active: boolean("api_active").default(false),
	display_mode: ctaDisplayModeEnum().default('footer'),
	content: jsonb(),
	timing_mode: ctaTimingModeEnum().notNull(),
	response_basis: ctaResponseBasisEnum().notNull(),
	response_number: integer().default(0),
	priority: integer().default(0),
	integrations: jsonb(),
	synced_at: timestamp("synced_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
}, (table) => [
	index("agent_ctas_agent_id_idx").on(table.agent_id),
]);

export const searches = pgTable("searches", {
	id: bigint({ mode: "number" }).default(sql`nextval('searches_id_seq'::regclass)`).notNull(),
	query: text().notNull(),
	filters: jsonb(),
	results: jsonb(),
	prompt_id: bigint("prompt_id", { mode: "number" }),
	agent_id: bigint({ mode: "number" }).notNull(),
	agent_token_id: bigint({ mode: "number" }),
	cached: boolean("cached").default(false).notNull(),
	searched_at: timestamp("searched_at", { precision: 6, mode: 'string' }).default(sql`(now() AT TIME ZONE 'utc'::text)`).notNull(),
}, (table) => [
	index("searches_prompt_id_idx").on(table.prompt_id),
	index("searches_agent_id_idx").on(table.agent_id),
	index("searches_cached_idx").on(table.cached),
]);
