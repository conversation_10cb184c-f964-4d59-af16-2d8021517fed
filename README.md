<p align="center"><a href="https://apologistproject.org" target="_blank"><img src="https://ignite.apologist.com/img/apologist-agent-dark.svg" width="300" alt="Apologist Ignite logo"></a></p>

## Introduction

**Apologist Agent** is a <a href="https://nextjs.org/" target="_blank">NextJS</a> app that provides an interface to query the fine-tuned Apologist model.

## Environment Setup

### <a href="https://www.postgresql.org/" target="_blank">Database (PostgreSQL)</a>
The application will need to connect to a PostgreSQL database. 

### NPM (Node Package Manager)
You will need to install <a href="https://www.npmjs.com/" target="_blank">NPM</a> to build frontend assets. If you don't already have it and it wasn't bundled with your base environment, install it now.

## Application Setup

### Clone this repository to your local machine
```
git clone https://github.com/apologist-project/apg-agent.git
```  

### Set the application environment variables
Create a .env and paste the contents of the example file from the Confluence page. 
**IMPORTANT:** You must set all the environment variables correctly before proceeding. Ask Jake for all the sensitive credentials.

### Install Javascript packages
Run the following to install the application's Javascript packages:
```
npm install
```

### Create the database schema
First, ensure your database server is running and configured correctly. Create a new local Postgres database and execute the SQL file on the setup Confluence page. Make sure to run this on an empty database.

### Run any pending migrations
The database schema may have changed since the seed SQL was run. Run any pending migrations before proceeding:
```
npm run migrate
```

## Running the Environment

### Ensure the web server and database are running
Before you try to access your application, make sure the services it depends on are installed and started.

### Start the server
If you just want to run Agent standalone, you can simply run this on the command line:

```
next dev
```

However, Agent can now run in authenticated mode as part of the Apologist ecosystem. To do so, run the dev server using HTTPS on a specific subdomain with a root domain that matches the other apps in the Apologist product suite. e.g., if Apologist Ignite is served off of the root domain `apologist.test`, then you can run Agent on `agent.apologist.test` like so:

```
next dev -H agent.apologist.test --experimental-https
```

### Make a database schema change
In order to make a database schema change:

1. Make schema changes in code in `db/schema.ts`.
2. Generate the migration:

        npx drizzle-kit generate --name="[name_of_migration]"

3. Run the migration:

        npm run migrate

### Compile a payload schema validation change
In order to make an endpoint payload schema validation change:

```
ajv compile -s schema.json -o validation.js
```
