/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme')

module.exports = {
    darkMode: ['selector', '.dark'],
    content: [
        "./app/**/*.{js,ts,jsx,tsx}",
        "./pages/**/*.{js,ts,jsx,tsx}",
        "./components/**/*.{js,ts,jsx,tsx}",
    ],
    theme: {
        extend: {
            fontFamily: {
                body: ['var(--body-font)',  ...defaultTheme.fontFamily.sans],
                display: ['var(--display-font)',  'sans-serif'],
            },
            colors: {
                primary: {
                    50: '#f2f0ff',
                    100: '#e8e4ff',
                    200: '#d4ccff',
                    300: '#b5a4ff',
                    400: '#9270ff',
                    500: '#7137ff',
                    600: '#630fff',
                    700: '#5500ff',
                    800: '#4600da',
                    900: '#330099',
                    950: '#21007a',
                },
            },
            keyframes: {
                gradient: {
                    "0%": { backgroundPosition: "0% 50%" },
                    "100%": { backgroundPosition: "100% 50%" },
                },
                slidein: {
                    from: {
                        opacity: "0",
                        transform: "translateY(-25px)",
                    },
                    to: {
                        opacity: "1",
                        transform: "translateY(0)",
                    },
                },
            },
            animation: {
                gradient: "gradient 8s linear infinite",
                "slidein-stagger-1": "slidein 1s ease 250ms forwards",
                "slidein-stagger-2": "slidein 1s ease 750ms forwards",
                "slidein-stagger-3": "slidein 1s ease 1250ms forwards",
                "slidein-stagger-4": "slidein 1s ease 1750ms forwards",
            },
        },
    },
    plugins: [
        // ...
        require('@tailwindcss/forms'),
        require("@designbycode/tailwindcss-text-shadow")({
            shadowColor: "rgba(0, 0, 0, 0.125)",
            shadowBlur: ".125rem",
            shadowOffsetX: "0",
            shadowOffsetY: ".125rem",
        }),
        require('@ceol/tailwind-tooltip'),
    ],
    safelist: [
        'sm:inline',
    ],
}
