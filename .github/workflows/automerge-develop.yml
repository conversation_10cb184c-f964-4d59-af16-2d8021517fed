name: Auto-Merge Main to Develop

on:
  push:
    branches:
      - main

jobs:
  update-develop:
    name: Merge main into develop after push
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2
      - name: merge
        uses: mtanzi/action-automerge@v1
        id: merge
        with:
          github_token: ${{ github.token }}
          source: 'main'
          target: 'develop'
