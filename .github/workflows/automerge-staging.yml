name: Auto-Merge Main to Staging

on:
  push:
    branches:
      - main

jobs:
  update-staging:
    name: Merge main into staging after push
    runs-on: ubuntu-latest
    steps:
      - name: checkout
        uses: actions/checkout@v2
      - name: merge
        uses: mtanzi/action-automerge@v1
        id: merge
        with:
          github_token: ${{ github.token }}
          source: 'main'
          target: 'staging'
