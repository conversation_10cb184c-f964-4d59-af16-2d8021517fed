import { NextResponse } from 'next/server'
import acceptLanguage from 'accept-language'
import { fallbackLng, languages, cookieName } from './app/i18n/settings'
import {
    getSupportedLanguages,
    agentIsInactive,
    debug
} from './app/lib/helpers';
import { getAgentByHost } from './app/lib/agent';
import { get503Response } from './app/lib/response';
import { decryptBackend } from "./app/lib/crypt";

export const config = {
    // matcher: '/:lng*'
    matcher: ['/((?!api|_next/static|_next/image|_vercel|assets|beacon|demo.html|favicon.ico|sw.js).*)']
}

export async function middleware(req) {

    const headers = new Headers(req.headers);

    // This is all really for the UI only; skip it for the API
    if (req.nextUrl.pathname.substring(0, 3).toLowerCase() !== 'api') {

        let agent;
        if (process.env.NEXT_PUBLIC_ENV !== 'local') {
            const { getAgentByHost } = await import('./app/lib/agent');
            agent = await getAgentByHost(req.headers.get('host'), true);
            console.log(agent);
            if (agentIsInactive(agent)) {
                return get503Response();
            }
        }

        if (agent && agent.has_basic_auth) {
            let authenticated = false;
            const basicAuth = req.headers.get("authorization");
            if (basicAuth) {
                const authValue = basicAuth.split(" ")[1];
                const [user, pwd] = atob(authValue).split(":");
                if ((user === agent.basic_auth_user) && (pwd === await decryptBackend(agent.basic_auth_password))) {
                    authenticated = true;
                }
            }
            if (!authenticated) {
                return new NextResponse("Unauthorized", {
                    status: 401,
                    headers: {
                        "WWW-Authenticate": 'Basic realm="Private Agent"'
                    }
                });
            }
        }

        // Store current request url in a custom header, which you can read later

        headers.set('x-url', req.url);

        // Store a couple query params b/c NextJS is dumb
        const queryParams = req.nextUrl.searchParams;
        headers.set('x-theme', queryParams.get('theme'));
        headers.set('x-color', queryParams.get('color'));

        acceptLanguage.languages(getSupportedLanguages(agent, languages));

        let lng;
        if (req.cookies.has(cookieName)) lng = acceptLanguage.get(req.cookies.get(cookieName).value);
        if (!lng) lng = acceptLanguage.get(req.headers.get('Accept-Language'));
        if (!lng && agent) lng = agent.default_language;
        if (!lng) lng = fallbackLng;

        // Redirect if lng in path is not supported
        if (
            !languages.some(loc => req.nextUrl.pathname.startsWith(`/${loc}`)) &&
            !req.nextUrl.pathname.startsWith('/_next')
        ) {
            return NextResponse.redirect(new URL(`/${lng}${req.nextUrl.pathname}`, req.url));
        }

        if (req.headers.has('referer')) {
            const refererUrl = new URL(req.headers.get('referer'));
            const lngInReferer = languages.find((l) => refererUrl.pathname.startsWith(`/${l}`));
            const response = NextResponse.next({ headers });
            if (lngInReferer) response.cookies.set(cookieName, lngInReferer);
            return response;
        }

        return NextResponse.next({ headers });

    }




}
