{"redirects": [{"source": "/:path*", "has": [{"type": "host", "value": "kids.apologist.ai"}], "destination": "https://kids.gospel.bot/:path*", "permanent": true}], "git": {"deploymentEnabled": {"main": false}}, "headers": [{"source": "/api/v1/(.*)", "headers": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS,PATCH,DELETE,POST,PUT"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, X-Api-Version, X-Api-Key, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, Authorization"}, {"key": "Access-Control-Expose-Headers", "value": "X-Completion"}]}]}