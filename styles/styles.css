@tailwind base;
@tailwind components;
@tailwind utilities;

#app {
    background-repeat: no-repeat;
    background-position: top center;
    background-size: 100% auto;
    background-attachment: fixed;
}

.font-display {
    letter-spacing: .01rem;
}

/* Dark mode fixes */
.dark .apg-message-user {
    background-color: rgba(0, 0, 0, 0.25);
}

/* response loader */
.apg-message-agent-loader {
    margin-top: 1rem;
}
.apg-message-agent-loader:after {
    overflow: hidden;
    display: inline-block;
    vertical-align: bottom;
    -webkit-animation: ellipsis steps(4, end) 1000ms infinite;
    animation: ellipsis steps(4, end) 1000ms infinite;
    content: "\2026";
    width: 0;
    @apply ml-1;
}
@keyframes ellipsis {
    to {
        width: 40px;
    }
}
@-webkit-keyframes ellipsis {
    to {
        width: 40px;
    }
}

/* response formatting */
.apg-message-agent-body p,
.apg-message-agent-body li
{
    margin-top: 1rem;
    margin-bottom: 1rem;
}
.apg-message-agent-body ul,
.apg-message-agent-body ol
{
    padding-left: 2rem;
}
.apg-message-agent-body ul {
    list-style: disc outside;
}
.apg-message-agent-body ol {
    list-style: decimal outside;
}
.apg-message-agent-body strong {
    @apply font-semibold;
}
.apg-message-agent-body em {
    @apply italic;
}
.apg-message-agent-body h3 {
    @apply font-bold;
    margin-bottom: 1rem;
}
.apg-message-agent-body h3 strong {
    @apply font-bold;
}
.apg-message-agent-body blockquote {
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-left: 2rem;
    padding-right: 2rem;
}
.apg-message-agent-body hr {
    margin-top: 2rem;
    margin-bottom: 2rem;
}
.apg-message-agent-body table {
    @apply w-full;
}
.apg-message-agent-body table th,
.apg-message-agent-body table td
{
    @apply border border-collapse border-gray-600 dark:border-gray-200 py-2 px-4;
}
.apg-message-agent-body table th {
    @apply bg-gray-200 dark:bg-gray-900/90;
}
.apg-message-agent-body a {
    @apply underline;
}

[dir="ltr"] .apg-action-btn:first-child button {
    @apply !pl-4 !rounded-l-full;
}
[dir="ltr"] .apg-action-btn:last-child button {
    @apply !pr-4 !rounded-r-full;
}
[dir="rtl"] .apg-action-btn:first-child button {
    @apply !pr-4 !rounded-r-full;
}
[dir="rtl"] .apg-action-btn:last-child button {
    @apply !pl-4 !rounded-l-full;
}

#apg-info-modal-body p {
    @apply mb-4;
}

/* TipTap editor sytle */
[data-as-button="true"] {
    @apply cursor-pointer inline-block py-2 px-4 rounded-full text-base shadow-md font-semibold text-white focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2;
}
[data-as-button-theme="secondary"] {
    @apply bg-gray-500 dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-400 dark:hover:bg-gray-700;
}
[data-as-button-theme="tertiary"] {
    @apply border border-gray-300 dark:border-gray-700 text-gray-400 dark:text-gray-600 hover:text-white;
}
