const apg = {

    ns: 'apg',
    path: '/beacon/',
    styles: 'styles.css',
    defaultLanguage: 'en',
    offsetX: null,
    offsetY: null,
    iconRadius: 20,

    script: null,
    baseUrl: null,
    icon: null,
    iframe: null,
    language: null,
    isMobile: (window.innerWidth < 481),
    qryParams: [
        'language',
        'languages',
        'translation',
        'translations',
        'theme',
        'color',
        'preamble',
        'headline',
        'description',
        'agent',
        'headless',
        'autoscroll',
        'user',
    ],

    init: function() {
        this.setScript();
        this.setBaseUrl();
        this.setLanguage();
        this.addStyles();
        this.setOffsets();
        this.addIframe();
        this.addIcon();
    },

    setScript: function() {
        this.script = document.getElementById(this.nsPrefix('beacon'));
        console.log(this.script.dataset);
    },

    setBaseUrl: function() {
        let i = this.script.src.indexOf('/', 10);
        this.baseUrl = this.script.src.substring(0, i);
    },

    getParam: function(param) {

        let val = null;
        if (this.script.dataset[param]) {
            val = encodeURIComponent(this.script.dataset[param]);
        } else {

            let i = this.script.src.indexOf(param + '=');
            if (i > -1) {
                let len = param.length;
                let stop = this.script.src.indexOf('&', i+len+1);
                if (stop > -1) {
                    val = this.script.src.substring(i+len+1, stop);
                } else {
                    val = this.script.src.substring(i+len+1);
                }
            }

        }

        return val;

    },

    setLanguage: function() {
        this.language = this.getParam('language');
        if (!this.language) {
            this.language = this.defaultLanguage;
        }
    },

    addStyles: function() {
        let $styles = document.createElement('link');
        $styles.rel = 'stylesheet';
        $styles.type = 'text/css';
        $styles.href = `${this.baseUrl}${this.path}${this.styles}`;
        document.body.append($styles);
    },

    setOffsets: function() {
        this.offsetX = this.getParam('offsetX');
        this.offsetY = this.getParam('offsetY');
    },

    addIframe: function() {

        this.iframe = document.createElement('iframe');
        this.addClass(this.iframe, this.nsPrefix('iframe'));
        this.addClass(this.iframe, this.nsPrefix('hidden'));
        if (this.isMobile) {
            this.addClass(this.iframe, this.nsPrefix('mobile'));
        }

        let src = `${this.baseUrl}/${this.language}`;
        let qryStr = '?parent_url=' + encodeURIComponent(window.location.href) +
            '&parent_host=' + encodeURIComponent(window.location.host);
        let $this = this;
        this.qryParams.forEach(function(key) {
            let val = $this.getParam(key);
            if (val) {
                qryStr += '&' + key + '=' + val;
            }
        });
        src += qryStr;

        if (this.offsetX) {
            this.iframe.style.right = this.offsetX;
        }
        if (this.offsetY) {
            this.iframe.style.bottom = this.offsetY;
        }

        this.iframe.src = src;
        document.body.append(this.iframe);

    },

    addIcon: function() {

        this.icon = document.createElement('a');
        this.addClass(this.icon, this.nsPrefix('icon'));
        if (this.isMobile) {
            this.addClass(this.icon, this.nsPrefix('mobile'));
        }
        this.icon.href = '#';

        let $this = this;
        this.icon.addEventListener('click', function(e) {
            e.preventDefault();
            $this.toggleClass($this.iframe, $this.nsPrefix('hidden'));
        });

        let iconColor = this.getParam('iconColor') ?? this.getParam('color');
        if (iconColor) {
            this.icon.style.backgroundColor = decodeURIComponent(iconColor);
        }

        let iconImage = this.getParam('iconImage');
        if (iconImage) {
            this.icon.style.backgroundImage = `url("${decodeURIComponent(iconImage)}")`;
        }

        if (this.offsetX) {
            this.icon.style.right = this.offsetX;
            this.icon.style.marginRight = (this.iconRadius * -1) + 'px';
        }
        if (this.offsetY) {
            this.icon.style.bottom = this.offsetY;
            this.icon.style.marginBottom = (this.iconRadius * -1) + 'px';
        }

        document.body.append(this.icon);

    },

    nsPrefix: function(str) {
        return `${this.ns}-${str}`;
    },

    addClass: function(el, str) {
        let classes = el.className.split(' ');
        classes.push(str);
        el.className = classes.join(' ');
    },

    removeClass: function(el, str) {
        el.className = el.className.replace(str, '');
    },

    toggleClass: function(el, str) {
        let classes = el.className.split(' ');
        if (classes.includes(str)) {
            this.removeClass(el, str);
        } else {
            this.addClass(el, str);
        }
    }

};

apg.init();
