import { Fragment } from 'react'
import { Menu, Transition } from '@headlessui/react'
import { LanguageIcon, ChevronDownIcon } from '@heroicons/react/24/solid';
import Link from 'next/link'
import { track } from '@vercel/analytics';
import { useSearchParams } from 'next/navigation';
import { redirectToUrl } from '../lib/request';

function classNames(...classes) {
    return classes.filter(Boolean).join(' ')
}

export default function LanguageSwitcher({ lng, t, supportedLanguages }) {

    const searchParams = useSearchParams();
    const languages = JSON.parse(process.env.NEXT_PUBLIC_AUTO_LANGUAGES);

    const handleLanguageOpen = () => {
        track('Language:Open');
    };

    const handleLanguageClose = () => {
        track('Language:Close');
    };

    const handleLanguageSwitch = (language) => {
        track('Language:Switch', { from: lng, to: language });
        redirectToUrl(language, new URLSearchParams(searchParams));
    };

    return (supportedLanguages.length > 1) && (

        <Menu
            id="apg-language-switcher"
            as="div"
            className="relative"
        >

            <div>
                <Menu.Button
                    className="
                        p-2
                        rounded-lg
                        flex
                        items-center
                        justify-center
                        gap-x-2
                        shadow-sm
                        dark:shadow-xl
                        bg-gray-200
                        dark:bg-gray-800
                        text-gray-900
                        dark:text-white
                        hover:bg-gray-300
                        dark:hover:bg-gray-700
                        opacity-75
                        hover:opacity-100
                    "
                    onClick={handleLanguageOpen}
                >
                    <LanguageIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" aria-hidden="true" />
                    <span className="sr-only">{t('languages.label')}</span>
                    <span>{languages[lng]}</span>
                    <ChevronDownIcon className="h-4 w-4 text-gray-600 dark:text-gray-400 relative top-0.5" aria-hidden="true" />
                </Menu.Button>
            </div>

            <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
            >

                <Menu.Items
                    className="
                        absolute
                        ltr:right-0
                        rtl:left-0
                        z-10
                        mt-2
                        w-52
                        max-h-96
                        overflow-y-auto
                        ltr:origin-top-right
                        rtl:origin-top-left
                        py-2
                        rounded-md
                        bg-white/95
                        dark:bg-gray-900/95
                        shadow-lg
                        ring-1
                        ring-white
                        dark:ring-black
                        ring-opacity-5
                        focus:outline-none
                        border
                        border-gray-100
                        dark:border-gray-800
                    "
                    onClose={handleLanguageClose}
                >

                    {supportedLanguages.filter(e => e !== lng).map((language) => (

                        <Menu.Item key={language}>
                            {({ active }) => (
                                <Link
                                    href="#"
                                    className={classNames(
                                        active ? 'bg-primary-500 text-white' : 'text-gray-800 dark:text-gray-300',
                                        'group flex items-center px-4 py-2 text-md'
                                    )}
                                    onClick={() => handleLanguageSwitch(language)}
                                >
                                    {languages[language]}
                                </Link>
                            )}
                        </Menu.Item>

                    ))}

                </Menu.Items>

            </Transition>

        </Menu>

    )
}


