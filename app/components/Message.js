"use client";

import {
    SparklesIcon,
    ChevronUpIcon,
    ChevronDownIcon
} from '@heroicons/react/24/outline';
import MessageActionsBar from './MessageActionsBar';
import MediaList from './MediaList';
import { transformCompletion } from '../lib/format';
import {
    useState,
    useEffect,
    useRef
} from 'react';
import { track } from '@vercel/analytics';
import {
    getMessageText,
    debug,
} from '../lib/helpers';
import Cta from './Cta';

const Message = ({
    t,
    lng,
    message,
    isCurrentPrompt,
    responding,
    agentId,
    userId,
    theme,
    realTimeTranslation,
    isEmbedded,
    showMedia,
    mediaCollectionIds,
}) => {

    const messageText = getMessageText(message);

    const [expanded, setExpanded] = useState(isCurrentPrompt);
    const [mediaItems, setMediaItems] = useState([]);
    const [hasSearchedMedia, setHasSearchedMedia] = useState(false);
    const [ctaOpen, setCtaOpen] = useState((message.metadata?.cta?.display_mode === 'modal'));
    const isUser = (message.role === 'user');

    useEffect(() => {
        setExpanded(isCurrentPrompt);
    }, [isCurrentPrompt]);

    const handleExpand = () => {
        track('Response:Expand', {promptId: message.metadata?.id});
        setExpanded(true);
    };

    const handleCollapse = () => {
        track('Response:Collapse', {promptId: message.metadata?.id});
        setExpanded(false);
    };

    const handleResponseClick = (e) => {
        if (e.target.tagName.toUpperCase() === 'A') {
            track('Response:LinkClick', {promptId: message.metadata?.id, href: e.target.getAttribute('href')});
            window.open(e.target.getAttribute('href'), '_blank');
            e.stopPropagation();
            e.preventDefault();
        }
    };

    let mediaFetch = useRef(false);
    useEffect(() => {
        if (showMedia && !isUser && message.metadata?.prompt && message.metadata?.id && !mediaFetch.hasRun) {
            const getMediaItems = async () => {

                try {

                    const languages = [process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE];
                    if (lng !== process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE) {
                        languages.push(lng);
                    }
                    const filters = {
                        model: 'source',
                        types: process.env.NEXT_PUBLIC_SEARCH_SOURCE_TYPES.split(',').map((x) => x.trim()),
                        languages: languages,
                    };
                    if (mediaCollectionIds?.length > 0) {
                        filters.collection_ids = mediaCollectionIds;
                    }

                    const response = await fetch(
                        '/api/v1/search',
                        {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                query: message.metadata?.prompt,
                                filters: filters,
                                prompt_id: message.metadata?.id,
                            }),
                        }
                    );

                    const json = await response.json();
                    setMediaItems(json.results);
                    setHasSearchedMedia(true);

                } catch (error) {
                    console.error("Error fetching data:", error);
                }

            };
            getMediaItems();
            mediaFetch.hasRun = true;
        }
    }, [message.metadata?.prompt, message.metadata?.id]);

    const handleCtaOpen = () => {
        setCtaOpen(true);
        track('Cta:Open', {promptId: message.metadata?.id, agentId: agentId, ctaId: message.metadata?.cta.id});
    };

    return (
        <>

            {isUser && (

                <div
                    className="
                        apg-message-user
                        relative
                        ltr:pl-4
                        rtl:pr-4
                        ltr:pr-12
                        rtl:pl-12
                        py-2
                        md:mx-2
                        mt-3
                        text-white
                        text-xl
                        md:rounded-3xl
                        bg-primary-500
                        dark:bg-gray-900/75
                    "
                >

                    {messageText.split("\n").map((text, index) => (text.length > 0) && (
                        <span key={index} className="min-w-0">
                            {text}
                        </span>
                    ))}

                    {isCurrentPrompt && responding && (
                        <SparklesIcon
                            className="
                                apg-message-user-icon
                                sparkle-icon
                                h-8
                                w-8
                                m-auto
                                text-white
                                dark:text-primary-500
                                animate-pulse
                                absolute
                                ltr:right-3
                                rtl:left-3
                                top-1.5
                            "
                            aria-hidden="true"
                        />
                    )}

                </div>

            )}

            {!isUser && (messageText.length > 0) && (

                <>

                    <div
                        className="
                            apg-message-agent
                            relative
                            px-4
                            md:px-6
                            text-gray-700
                            dark:text-gray-300
                        "
                    >

                        {!isCurrentPrompt && (
                            <div
                                className={`
                                w-full
                                h-11
                                m-auto
                                absolute
                                left-0
                                ${expanded ? '-top-[3.875rem]' : '-top-[2.75rem]'}
                            `}
                            >
                                <button
                                    className={`
                                    apg-message-agent-expand
                                    h-full
                                    w-full
                                    ${expanded ? 'hidden' : ''}
                                `}
                                    onClick={handleExpand}
                                    title={t('response.show')}
                                >
                                    <ChevronDownIcon
                                        className="
                                        apg-message-user-icon
                                        chevron-down-icon
                                        absolute
                                        top-3
                                        ltr:right-5
                                        rtl:left-5
                                        w-6
                                        h-6
                                        text-white
                                        dark:text-primary-500
                                    "
                                        aria-hidden="true"
                                    />
                                </button>
                                <button
                                    className={`
                                    apg-message-agent-contract
                                    h-full
                                    w-full
                                    ${!expanded ? 'hidden' : ''}
                                `}
                                    onClick={handleCollapse}
                                    title={t('response.hide')}
                                >
                                    <ChevronUpIcon
                                        className="
                                        apg-message-user-icon
                                        chevron-down-icon
                                        absolute
                                        top-3
                                        ltr:right-5
                                        rtl:left-5
                                        w-6
                                        h-6
                                        text-white
                                        dark:text-primary-500
                                    "
                                        aria-hidden="true"
                                    />
                                </button>
                            </div>
                        )}

                        {expanded && (
                            <div
                                className="apg-message-agent-body"
                                dangerouslySetInnerHTML={{
                                    __html: realTimeTranslation ?
                                        messageText :
                                        transformCompletion(messageText)
                                }}
                                onClick={handleResponseClick}
                            >
                            </div>
                        )}

                    </div>

                    {expanded && (
                        <div
                            className="
                                apg-message-agent-footer
                                text-gray-700
                                dark:text-gray-300
                            "
                        >

                            {(!isCurrentPrompt || !responding) && (
                                <MessageActionsBar
                                    t={t}
                                    message={message}
                                    messageText={messageText}
                                    isCurrentPrompt={isCurrentPrompt}
                                    responding={responding}
                                    agentId={agentId}
                                    userId={userId}
                                    theme={theme}
                                    isEmbedded={isEmbedded}
                                />
                            )}

                            {showMedia && !isUser && (
                                <MediaList
                                    t={t}
                                    mediaItems={mediaItems}
                                    hasSearchedMedia={hasSearchedMedia}
                                    responding={responding}
                                    isCurrentPrompt={isCurrentPrompt}
                                    promptId={message.metadata?.id}
                                    userId={userId}
                                />
                            )}

                            {!responding && message.metadata?.cta && (
                                <Cta
                                    t={t}
                                    lng={lng}
                                    agentId={agentId}
                                    promptId={message.metadata?.id}
                                    cta={message.metadata?.cta}
                                    ctaOpen={ctaOpen}
                                    setCtaOpen={setCtaOpen}
                                    handleCtaOpen={handleCtaOpen}
                                />
                            )}

                        </div>
                    )}

                </>

            )}

        </>
    );
};

export default Message;
