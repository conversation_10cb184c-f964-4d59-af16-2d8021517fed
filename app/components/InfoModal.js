"use client"

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { MagnifyingGlassCircleIcon, XMarkIcon} from '@heroicons/react/24/outline';
import APImgLight from '../../public/assets/images/apologist-project-light.svg';
import APImgDark from '../../public/assets/images/apologist-project-dark.svg';
import Link from 'next/link';
import { track } from '@vercel/analytics';

export default function InfoModal({
    t,
    open,
    setOpen,
    theme,
    agentCreatorName,
    agentImage,
    agentDescription,
    agentCreatorUrl,
    isOpenSource,
}) {

    const handleCtaClick = () => {
        track('Info:CTA'); // Track the Footer: Sponsor event
    };

    const handleClose = () => {
        setOpen(false);
        track('Info:Close'); // Track the Help: Close event
    };

    return (

        <Transition.Root show={open} as={Fragment}>
            <Dialog
                id="apg-info-modal"
                as="div"
                className={`${theme} relative z-10`}
                onClose={handleClose}
            >

                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-white/90 dark:bg-gray-900/90 bg-opacity-100 transition-opacity" />
                </Transition.Child>

                <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                    <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">

                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            enterTo="opacity-100 translate-y-0 sm:scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                        >
                            <Dialog.Panel
                                className="
                                    relative
                                    transform
                                    overflow-hidden
                                    md:min-w-sm
                                    md:max-w-md
                                    rounded-lg
                                    border
                                    my-8
                                    p-6
                                    text-left
                                    shadow-xl
                                    transition-all
                                    border-gray-200
                                    dark:border-gray-800
                                    bg-white/90
                                    dark:bg-gray-900/90
                                    text-gray-900
                                    dark:text-white
                                "
                            >

                                <Dialog.Title
                                    id="apg-info-modal-header"
                                    as="h3"
                                    className="text-md font-semibold leading-6 text-2xl mb-6 text-center"
                                >

                                    <Link
                                        href={agentCreatorUrl ?? process.env.NEXT_PUBLIC_SPONSOR_URL}
                                        title={agentCreatorName ?? t('about.title')}
                                        target="_blank"
                                    >

                                        {agentImage ? (
                                            <img
                                                id="apg-info-modal-image"
                                                src={agentImage}
                                                alt={agentCreatorName ?? t('about.title')}
                                                className="mx-auto max-w-32 max-h-16"
                                            />
                                        ) : (
                                            <>
                                                <APImgDark
                                                    className="h-16 mx-auto hidden dark:block text-primary-500"
                                                    alt={t('about.title')}
                                                    width="200"
                                                    height="100"
                                                />
                                                <APImgLight
                                                    className="h-16 mx-auto dark:hidden text-primary-500"
                                                    alt={t('about.title')}
                                                    width="200"
                                                    height="100"
                                                />
                                            </>
                                        )}

                                    </Link>

                                </Dialog.Title>

                                {agentDescription ? (
                                    <div
                                        id="apg-info-modal-body"
                                        dangerouslySetInnerHTML={{ __html: agentDescription }}
                                    >
                                    </div>
                                ) : (
                                    <>
                                        <p className="text-md text-gray-800 dark:text-gray-300 mb-4">{t('about.intro1')}</p>
                                        <p className="text-md text-gray-800 dark:text-gray-300 mb-4">{t('about.intro2')}</p>
                                    </>
                                )}

                                <a
                                    id="apg-info-modal-cta"
                                    href={agentCreatorUrl ?? process.env.NEXT_PUBLIC_SPONSOR_URL}
                                    target="_blank"
                                    className="
                                        block
                                        text-center
                                        w-full
                                        rounded-full
                                        bg-primary-500
                                        border
                                        border-primary-500
                                        px-2
                                        py-2
                                        mt-6
                                        text-md
                                        font-semibold
                                        text-white
                                        hover:bg-primary-600
                                        focus-visible:outline
                                        focus-visible:outline-2
                                        focus-visible:outline-offset-2
                                        focus-visible:outline-primary-500
                                    "
                                    onClick={handleCtaClick}
                                >
                                    {t('about.cta')}
                                </a>

                                {isOpenSource && (
                                    <div className="text-center mt-6">
                                        <span
                                            className="
                                                inline-flex
                                                items-center
                                                rounded-md
                                                bg-gray-50
                                                dark:bg-gray-400/10
                                                px-2
                                                py-1
                                                text-sm
                                                font-medium
                                                text-gray-600
                                                dark:text-gray-400
                                                ring-1
                                                ring-gray-500/10
                                                dark:ring-gray-400/20
                                                ring-inset
                                                uppercase
                                            "
                                        >
                                            <MagnifyingGlassCircleIcon className="h-5 w-5 mr-1" aria-hidden="true"/>
                                            {t('about.open')}
                                        </span>
                                    </div>
                                )}

                                <button
                                    type="button"
                                    className="
                                        absolute
                                        top-1
                                        ltr:right-0
                                        rtl:left-0
                                        p-3
                                        text-gray-400
                                        hover:text-gray-600
                                        dark:text-gray-600
                                        dark:hover:text-gray-400
                                    "
                                    onClick={handleClose}
                                    title={t('navigation.close')}
                                >
                                    <XMarkIcon className="w-6 h-6" />
                                </button>

                            </Dialog.Panel>
                        </Transition.Child>

                    </div>
                </div>

            </Dialog>
        </Transition.Root>

    )
}
