'use client';

import { track } from '@vercel/analytics';

export default function Footer({
    t,
    isEmbedded,
    footerText,
    hideFooterCta,
    footerCtaLabel,
    footerCtaUrl,
}) {

    const handleSponsorClick = () => {
        track('Footer:Sponser');
    };

    const handleCtaClick = () => {
        track('Footer:Cta');
    };

    return (

        <footer id="apg-footer" className="flex justify-center items-center text-center grow-0 h-12 md:h-16 gap-x-2">

            <p
                id="apg-footer-text"
                className="text-center text-gray-400 dark:text-gray-600 text-sm sm:text-md"
                dangerouslySetInnerHTML={{ __html: footerText ?? t('footer.text') }}
            >
            </p>

            {!isEmbedded && !hideFooterCta && (
                <a
                    id="apg-footer-cta"
                    href={(footerCtaUrl && (footerCtaUrl.trim().length > 0)) ? footerCtaUrl : process.env.NEXT_PUBLIC_DONATE_URL}
                    className="
                        inline-flex
                        w-auto
                        items-center
                        justify-center
                        rounded-full
                        border
                        border-gray-300
                        dark:border-gray-700
                        px-3
                        py-1
                        text-md
                        font-semibold
                        text-gray-400
                        dark:text-gray-600
                        hover:border-primary-500
                        hover:bg-primary-500
                        hover:text-white
                        focus-visible:outline
                        focus-visible:outline-2
                        focus-visible:outline-offset-2
                        focus-visible:outline-primary-500
                    "
                    target="_blank"
                    onClick={handleCtaClick}
                >
                    <span className="inline-block">{(footerCtaLabel && (footerCtaLabel.trim().length > 0)) ? footerCtaLabel : t('footer.cta')}</span>
                </a>
            )}

        </footer>

    );
};
