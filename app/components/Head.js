import {
    getMediaUrl,
    getDbTranslation,
    parseFontFromUrl,
    getColor,
    debug,
} from '../lib/helpers';
import tinycolor from 'tinycolor2';
import { headers } from 'next/headers';

export default async function Head({
    lng,
    agent,
    theme
}) {

    const headersList = await headers();

    const agentFavicon = agent.favicon_path ? getMediaUrl(agent.favicon_path) : null;
    const agentIcon = agent.icon_path ? getMediaUrl(agent.icon_path) : null;

    // White label appearance
    const wlColor = getColor(headersList.get('x-color'), agent.primary_color);
    const bgColor = agent.background_color;
    const lighterColor = tinycolor(wlColor).brighten((theme === 'dark') ? 25 : 50).toString();
    const darkerColor = tinycolor(wlColor).darken(12.5).toString();
    const bgColorStr = bgColor ? `background: ${bgColor} !important;` : '';
    const displayFont = agent.display_font_name ?? parseFontFromUrl(agent.display_font_url ?? process.env.NEXT_PUBLIC_FONT_DISPLAY_URL);
    const bodyFont = agent.body_font_name ?? parseFontFromUrl(agent.body_font_url ?? process.env.NEXT_PUBLIC_FONT_BODY_URL);
    const wlStyle = `
        :root{
            --display-font: "${displayFont}";
            --body-font: "${bodyFont}";
        }
        .text-primary-500,
        .hover\\:text-primary-500:hover,
        .text-primary,
        .hover\\:text-primary,
        .dark .dark\\:text-primary,
        .dark .dark\\:text-primary-500
        {
            color: ${wlColor};
        }
        .bg-primary-500,
        .hover\\:bg-primary-500:hover,
        .hover\\:bg-primary-600:hover,
        .data-\\[focus\\]\\:bg-primary-500[data-focus],
        [data-as-button-theme="primary"],
        [data-as-button-theme="accent"]
        {
            background-color: ${wlColor};
        }
        [data-as-button-theme="primary"]:hover,
        [data-as-button-theme="accent"]:hover
        {
            background-color: ${darkerColor};
        }
        [data-as-button-theme="tertiary"]:hover {
            background-color: ${wlColor};
            border-color: ${wlColor};
        }
        .hover\\:bg-primary-600:hover {
            opacity: .75;
        }
        .border-primary-500,
        .hover\\:border-primary-500:hover
        {
            border-color: ${wlColor};
        }
        .focus\\:ring-primary-500:focus-visible,
        .focus-visible\\:outline-primary-500:focus-visible
        {
            --tw-ring-color: ${wlColor};
        }
        .dark .sparkle-icon {
            color: ${wlColor} !important;
        }
        .gradient-text {
            background: linear-gradient(
                to right,
                ${darkerColor},
                ${wlColor},
                ${lighterColor},
                ${wlColor},
                ${darkerColor},
                ${wlColor},
                ${lighterColor}
            );
            background-size: 300% 300%;
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
        }   
        #app {
            ${bgColorStr}
        }
        ${agent.custom_styles ?? ''}
    `;

    return (

        <head>

            <meta charSet="UTF-8"/>

            <title>{getDbTranslation(agent.meta_title, lng) ?? agent.name ?? process.env.NEXT_PUBLIC_META_TITLE}</title>

            {agent.meta_description && (
                <meta name="description" content={getDbTranslation(agent.meta_description, lng)}/>
            )}

            {agent.meta_keywords && (
                <meta name="keywords" content={getDbTranslation(agent.meta_keywords, lng)}/>
            )}

            <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
            <link rel="apple-touch-icon" href={agentIcon ?? '/assets/images/pwa-192x192.png'}/>
            <link rel="mask-icon" href={agentFavicon ?? '/assets/images/safari-pinned-tab.svg'} color={agent.icon_color ?? '#00aba9'}/>
            <link rel="icon" type="image/x-icon" href={agentFavicon ?? '/assets/images/safari-pinned-tab.svg'}/>

            <link rel="preconnect" href="https://fonts.googleapis.com"/>
            <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true"/>
            <link href={agent.display_font_url ?? process.env.NEXT_PUBLIC_FONT_DISPLAY_URL} rel="stylesheet" id="apg-display-font-link"/>
            <link href={agent.body_font_url ?? process.env.NEXT_PUBLIC_FONT_BODY_URL} rel="stylesheet" id="apg-body-font-link"/>

            <meta name="msapplication-TileColor" content={agent.icon_color ?? '#00aba9'}/>

            <style type="text/css" dangerouslySetInnerHTML={{ __html: wlStyle }}></style>

        </head>

    );

}
