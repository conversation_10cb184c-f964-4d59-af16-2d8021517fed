import { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import Link from 'next/link';
import { track } from '@vercel/analytics';

function classNames(...classes) {
    return classes.filter(Boolean).join(' ')
}

const items = [

    {
        title: 'My Profile',
        iconPaths: ['M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-5.5-2.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM10 12a5.99 5.99 0 0 0-4.793 2.39A6.483 6.483 0 0 0 10 16.5a6.483 6.483 0 0 0 4.793-2.11A5.99 5.99 0 0 0 10 12Z'],
        href: process.env.NEXT_PUBLIC_PROFILE_URL,
        classes: 'border-b border-gray-800',
        newTab: false,
        eventName: 'Profile',
    },

    {
        title: 'Administration',
        iconPaths: ['M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z'],
        href: process.env.NEXT_PUBLIC_ADMIN_URL,
        newTab: false,
        eventName: 'Admin',
    },

    {
        title: 'Knowledge Base ↗',
        iconPaths: ['M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z'],
        href: process.env.NEXT_PUBLIC_DOCS_URL,
        newTab: true,
        eventName: 'Docs',
    },

    {
        title: 'Contact Support ↗',
        iconPaths: ['M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75'],
        href: process.env.NEXT_PUBLIC_SUPPORT_URL,
        newTab: false,
        eventName: 'Support',
    },

    {
        title: 'Sign Out',
        iconPaths: [
            'M3 4.25A2.25 2.25 0 0 1 5.25 2h5.5A2.25 2.25 0 0 1 13 4.25v2a.75.75 0 0 1-1.5 0v-2a.75.75 0 0 0-.75-.75h-5.5a.75.75 0 0 0-.75.75v11.5c0 .414.336.75.75.75h5.5a.75.75 0 0 0 .75-.75v-2a.75.75 0 0 1 1.5 0v2A2.25 2.25 0 0 1 10.75 18h-5.5A2.25 2.25 0 0 1 3 15.75V4.25Z',
            'M19 10a.75.75 0 0 0-.75-.75H8.704l1.048-.943a.75.75 0 1 0-1.004-1.114l-2.5 2.25a.75.75 0 0 0 0 1.114l2.5 2.25a.75.75 0 1 0 1.004-1.114l-1.048-.943h9.546A.75.75 0 0 0 19 10Z',
        ],
        href: process.env.NEXT_PUBLIC_LOGOUT_URL,
        classes: 'border-t border-gray-800',
        newTab: false,
        eventName: 'Logout',
    },

];

const handleOpen = () => {
    track('User:Open');
};

const handleLinkClick = (eventName) => {
    track(`User:${eventName}`);
};

export default function UserMenu({ lng, t, user }) {
    const csrfToken = user.csrf_token;
    return (

        <Menu as="div" className="relative inline-block text-left">

            <div>
                <Menu.Button
                    className="
                        p-2
                        rounded-md
                        hover:bg-black/5
                        dark:hover:bg-white/5
                    "
                    onClick={handleOpen}
                >
                    <img
                        className="object-cover object-center rounded-full h-8 w-8"
                        src={user.avatar_url}
                        alt={user.name}
                    />
                </Menu.Button>
            </div>

            <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
            >

                <Menu.Items
                    className="
                        absolute
                        ltr:right-0
                        rtl:left-0
                        z-10
                        mt-2
                        w-48
                        ltr:origin-top-right
                        rtl:origin-top-left
                        rounded-md
                        ring-1
                        ring-opacity-5
                        focus:outline-none
                        border
                        text-sm
                        bg-white
                        dark:bg-gray-900
                        shadow-md
                        dark:shadow-xl
                        ring-white
                        dark:ring-black
                        border-white
                        dark:border-gray-800
                    "
                >

                    {items.map((item, i) => (

                        <Menu.Item key={i}>

                            <div className={(item.classes ? item.classes : '') + ' p-1'}>

                                <Link
                                    href={item.href}
                                    className="flex w-full items-center gap-2 whitespace-nowrap rounded-md p-2 transition-colors duration-75 outline-none disabled:pointer-events-none hover:bg-black/5 dark:hover:bg-white/5 focus-visible:bg-black/5 dark:focus-visible:bg-white/5"
                                    target={item.newTab ? '_blank' : '_self'}
                                    onClick={() => { handleLinkClick(item.eventName) }}
                                >
                                    <svg className="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                                        {item.iconPaths.map((path, i) => (
                                            <path key={i} strokeLinecap="round" strokeLinejoin="round" d={path}></path>
                                        ))}
                                    </svg>
                                    <span className="flex-1 truncate text-start text-gray-800 dark:text-gray-200">
                                        {item.title}
                                    </span>
                                </Link>

                            </div>

                        </Menu.Item>

                    ))}
                </Menu.Items>

            </Transition>

        </Menu>

    )
}


