"use client";

import { track } from '@vercel/analytics';
import {trackCta, trackView} from '../lib/tracking';
import {
    getConversationId,
    getDeviceId,
    getShareUrl,
    replaceTokens,
} from '../lib/helpers';
import {
    Fragment,
} from 'react';
import {
    Dialog,
    Transition
} from '@headlessui/react';

export default function Cta({
    t,
    lng,
    promptId,
    agentId,
    userId,
    cta,
    ctaOpen,
    setCtaOpen,
    handleCtaOpen,
}) {

    const handleCtaClick = () => {
        track('Cta:Click', {agentId: agentId, promptId: promptId, ctaId: cta.id});
        trackCta(promptId, agentId, cta.id);
    };

    const handleCtaClose = () => {
        setCtaOpen(false);
        track('Cta:Close', {promptId: promptId, agentId: agentId, ctaId: cta.id});
    };

    let content = cta.content;

    if (content) {
        content = replaceTokens(
            content,
            {
                user: userId,
                conversation: getConversationId(),
                device: getDeviceId(),
                share_url: encodeURIComponent(getShareUrl(agentId, getConversationId(), promptId)),
            }
        );
    }

    return (

        <>

            {(cta?.display_mode === 'modal') ? (

                <>

                    <div className="w-full text-center">
                        <button
                            className="
                                apg-message-agent-cta-modal-open
                                text-center
                                rounded-full
                                bg-primary-500
                                border
                                border-primary-500
                                px-4
                                py-2
                                text-md
                                font-semibold
                                text-white
                                hover:bg-primary-600
                                focus-visible:outline
                                focus-visible:outline-2
                                focus-visible:outline-offset-2
                                focus-visible:outline-primary-500
                                mx-auto
                                mb-8
                            "
                            onClick={handleCtaOpen}
                        >
                            {t('about.cta')}
                        </button>
                    </div>

                    <Transition.Root show={ctaOpen} as={Fragment}>
                        <Dialog
                            as="div"
                            className="relative z-10 apg-message-agent-cta-modal"
                            onClose={handleCtaClose}
                        >

                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0"
                                enterTo="opacity-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100"
                                leaveTo="opacity-0"
                            >
                                <div
                                    className="
                                        fixed
                                        inset-0
                                        bg-opacity-100
                                        transition-opacity
                                        bg-white/90
                                        dark:bg-gray-900/90
                                    "
                                />
                            </Transition.Child>

                            <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                                <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">

                                    <Transition.Child
                                        as={Fragment}
                                        enter="ease-out duration-300"
                                        enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                        enterTo="opacity-100 translate-y-0 sm:scale-100"
                                        leave="ease-in duration-200"
                                        leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                                        leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                    >

                                        <Dialog.Panel
                                            className="
                                                relative
                                                transform
                                                overflow-hidden
                                                w-full
                                                md:max-w-xl
                                                rounded-lg
                                                border
                                                my-8
                                                p-6
                                                text-center
                                                shadow-xl
                                                transition-all
                                                border-gray-200
                                                dark:border-gray-800
                                                bg-white/90
                                                dark:bg-gray-900/90
                                                text-gray-900
                                                dark:text-white
                                            "
                                        >

                                            <div
                                                className="apg-message-agent-cta-modal-body"
                                                onClick={handleCtaClick}
                                                dangerouslySetInnerHTML={{__html: content}}
                                            >
                                            </div>

                                            <div className="mt-4 sm:mt-8 flex gap-6 apg-message-agent-cta-modal-footer">
                                                <button
                                                    type="button"
                                                    className="
                                                        apg-media-modal-close
                                                        w-full
                                                        rounded-full
                                                        border
                                                        p-2
                                                        text-md
                                                        font-semibold
                                                        focus-visible:outline
                                                        focus-visible:outline-2
                                                        focus-visible:outline-offset-2
                                                        focus-visible:outline-primary-500
                                                        border-gray-400
                                                        dark:border-gray-600
                                                        text-gray-400
                                                        dark:text-gray-600
                                                        hover:border-gray-600
                                                        dark:hover:border-gray-400
                                                        hover:text-gray-600
                                                        dark:hover:text-gray-400
                                                    "
                                                    onClick={handleCtaClose}
                                                >
                                                    {t('navigation.close')}
                                                </button>
                                            </div>

                                        </Dialog.Panel>

                                    </Transition.Child>

                                </div>
                            </div>

                        </Dialog>
                    </Transition.Root>

                </>

            ) : (

                <div
                    onClick={handleCtaClick}
                    className="
                        apg-message-agent-cta
                        bg-gray-100
                        dark:bg-black/25
                        text-gray-600
                        dark:text-gray-400
                        text-center
                    "
                >
                    <div
                        className="mx-auto inline-block text-left"
                        dangerouslySetInnerHTML={{__html: content}}
                    >
                    </div>
                </div>

            )}

        </>

    );
};
