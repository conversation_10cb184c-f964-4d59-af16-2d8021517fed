"use client";

import InfoImage from './InfoImage';
import TranslationSwitcher from './TranslationSwitcher';
import LanguageSwitcher from './LanguageSwitcher';
import SearchableLanguageSwitcher from './SearchableLanguageSwitcher';

export default function AnonymousHeader({
    t,
    lng,
    translation,
    isEmbedded,
    theme,
    supportedLanguages,
    supportedTranslations,
    agentCreatorName,
    agentImage,
    agentDescription,
    agentCreatorUrl,
    isOpenSource,
}) {

    const hasOptions = ((Object.keys(supportedTranslations).length !== 1) || (supportedLanguages?.length !== 1));

    return (

        <header id="apg-header" className="flex-none">

            <div
                className="
                    flex
                    items-center
                    gap-x-4
                    h-20
                "
            >

                <div
                    className={`
                        flex 
                        items-center 
                        justify-center 
                        h-full 
                        ${!hasOptions ? 'mx-auto' : ''}
                    `}
                >
                    <InfoImage
                        t={t}
                        isEmbedded={isEmbedded}
                        theme={theme}
                        agentCreatorName={agentCreatorName}
                        agentImage={agentImage}
                        agentDescription={agentDescription}
                        agentCreatorUrl={agentCreatorUrl}
                        isOpenSource={isOpenSource}
                    />
                </div>

                {hasOptions && (
                    <div
                        className="
                            flex
                            gap-x-2
                            ltr:ml-auto
                            rtl:mr-auto
                        "
                    >

                        <TranslationSwitcher t={t} translation={translation} supportedTranslations={supportedTranslations} />

                        {(supportedLanguages.length >= process.env.NEXT_PUBLIC_LANGUAGES_SEARCHABLE_THRESHOLD) ? (
                            <SearchableLanguageSwitcher lng={lng} t={t} supportedLanguages={supportedLanguages} theme={theme} />
                        ) : (
                            <LanguageSwitcher lng={lng} t={t} supportedLanguages={supportedLanguages} />
                        )}

                    </div>
                )}

            </div>

        </header>

    );
};
