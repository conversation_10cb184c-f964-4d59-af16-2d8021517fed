import { Fragment } from 'react'
import { Menu, Transition } from '@headlessui/react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronUpDownIcon } from '@heroicons/react/24/outline';
import AgentImgDark from '../../public/assets/images/apologist-agent-dark.svg';
import IgniteImgDark from '../../public/assets/images/apologist-ignite-dark.svg';
import BeaconImgDark from '../../public/assets/images/apologist-beacon-dark.svg';
import SocialImgDark from '../../public/assets/images/apologist-social-dark.svg';
import PocketImgDark from '../../public/assets/images/apologist-pocket-dark.svg';
import AgentImgLight from '../../public/assets/images/apologist-agent-light.svg';
import IgniteImgLight from '../../public/assets/images/apologist-ignite-light.svg';
import BeaconImgLight from '../../public/assets/images/apologist-beacon-light.svg';
import SocialImgLight from '../../public/assets/images/apologist-social-light.svg';
import PocketImgLight from '../../public/assets/images/apologist-pocket-light.svg';
import { track } from '@vercel/analytics';

function classNames(...classes) {
    return classes.filter(Boolean).join(' ')
}

export default function ProductSwitcher({ lng, t, isEmbedded, center }) {

    const products = [

        {
            name: 'Apologist Beacon',
            lightImg: BeaconImgLight,
            darkImg: BeaconImgDark,
            url: process.env.NEXT_PUBLIC_BEACON_URL,
            description: 'Embed Agent on Your Site',
            newTab: true,
        },

        {
            name: 'Apologist Social',
            lightImg: SocialImgLight,
            darkImg: SocialImgDark,
            url: process.env.NEXT_PUBLIC_SOCIAL_URL,
            description: 'Community-Led Discourse',
            newTab: true,
        },

        {
            name: 'Apologist Ignite',
            lightImg: IgniteImgLight,
            darkImg: IgniteImgDark,
            url: process.env.NEXT_PUBLIC_IGNITE_URL,
            description: 'Promote Your Ministry',
            newTab: false,
        },

        // {
        //     name: 'Apologist Pocket',
        //     lightImg: pocketImgLight,
        //     darkImg: pocketImgDark,
        //     img: pocketImg,
        //     url: process.env.NEXT_PUBLIC_POCKET_URL,
        //     description: 'Apologetics on the Go',
        //     newTab: true,
        // },

    ];

    const handleProductsOpen = () => {
        track('Products:Open');
    };

    const handleProductsSelect = (productName) => {
        track('Products:Select', { product: productName });
    };

    return (
        <Menu
            id="apg-product-switcher"
            as="div"
        >
            <div className={(!center ? 'bg-white/90 dark:bg-gray-900/90' : '')}>
                <Menu.Button
                    className={(center ? 'mx-auto' : '') + ' shrink-0 py-2 flex items-center gap-x-2 cursor-pointer opacity-50 hover:opacity-100'}
                    onClick={handleProductsOpen}
                >

                    {isEmbedded ? (
                        <BeaconImgDark className="h-12 w-auto flex hidden dark:block text-primary-500" title={t('app')} />
                    ) : (
                        <AgentImgDark className="h-12 w-auto flex hidden dark:block text-primary-500" title={t('app')} />
                    )}

                    {isEmbedded ? (
                        <BeaconImgLight className="h-12 w-auto flex dark:hidden text-primary" title={t('app')} />
                    ) : (
                        <AgentImgLight className="h-12 w-auto flex dark:hidden text-primary" title={t('app')} />
                    )}

                    {!isEmbedded && (
                        <ChevronUpDownIcon className="h-5 w-5 text-gray-600 dark:text-gray-300 hover:text-gray-500 dark:hover:text-gray-400" aria-hidden="true" />
                    )}

                </Menu.Button>
            </div>

            {!isEmbedded && (

                <Transition
                    as={Fragment}
                    enter="transition ease-out duration-100"
                    enterFrom="transform opacity-0 scale-95"
                    enterTo="transform opacity-100 scale-100"
                    leave="transition ease-in duration-75"
                    leaveFrom="transform opacity-100 scale-100"
                    leaveTo="transform opacity-0 scale-95"
                >
                    <Menu.Items className={(center ? 'absolute left-1/2 ltr:-ml-28 rtl:-mr-28' : 'ltr:-ml-3 rtl:-mr-3 text-left relative') + ' z-10 w-56 origin-top-right p-1 rounded-lg bg-white/95 dark:bg-gray-900/95 shadow-lg ring-1 ring-white dark:ring-black ring-opacity-5 focus:outline-none border border-white dark:border-gray-800'}>

                        {products.map((product) => (

                            <Menu.Item key={product.name} className="relative">
                                {({ active }) => (

                                    <Link
                                        href={product.url}
                                        className="block p-2 hover:bg-gray-100/95 dark:hover:bg-gray-800/95 rounded-md"
                                        target={center || product.newTab ? '_blank' : '_self'}
                                        onClick={() => {
                                            handleProductsSelect(product.name)
                                        }}
                                    >


                                        <product.darkImg
                                            className={(center ? 'mx-auto' : '') + ' h-12 w-auto hidden dark:block text-primary-500'}
                                            alt={product.name}
                                        />
                                        <product.lightImg
                                            className={(center ? 'mx-auto' : '') + ' h-12 w-auto dark:hidden text-primary-500'}
                                            alt={product.name}
                                        />

                                        <p className={(center ? 'text-center' : 'text-left') + ' mt-2 text-sm text-gray-600 dark:text-gray-400'}>
                                            {product.description}
                                        </p>

                                        {!center && product.newTab && (
                                            <svg
                                                className="flex-none h-5 w-5 text-gray-600 dark:text-gray-400 absolute top-2 ltr:right-2 rtl:left-2"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                strokeWidth="1.5" stroke="currentColor" aria-hidden="true"
                                                data-slot="icon">
                                                <path strokeLinecap="round" strokeLinejoin="round"
                                                      d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"></path>
                                            </svg>
                                        )}

                                    </Link>

                                )}
                            </Menu.Item>

                        ))}

                    </Menu.Items>
                </Transition>

            )}

        </Menu>
    )
}


