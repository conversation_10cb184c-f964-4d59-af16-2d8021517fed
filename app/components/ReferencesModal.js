"use client"

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { ArrowRightCircleIcon } from '@heroicons/react/20/solid';
import { dedupeArray, debug } from '../lib/helpers';
import { track } from '@vercel/analytics';
import { getSourceReferralUrl } from '../lib/tracking';

export default function ReferencesModal({
    lng,
    t,
    open,
    setOpen,
    sources,
    userId,
    theme,
    promptId
}) {

    const handleReferClick = (source) => {
        track('Sources:Refer', {sourceId: source.id}); // Track the Sources: Refer event
    };

    const handleClose = () => {
        setOpen(false);
        track('Sources:Close'); // Track the Sources: Close event
    };

    return (

        <Transition.Root show={open} as={Fragment}>
            <Dialog
                as="div"
                className={`${theme} relative z-10 apg-references-modal`}
                onClose={handleClose}
            >

                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div
                        className="
                            fixed
                            inset-0
                            bg-opacity-100
                            transition-opacity
                            bg-white/90
                            dark:bg-gray-900/90
                        "
                    />
                </Transition.Child>

                <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                    <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">

                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            enterTo="opacity-100 translate-y-0 sm:scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                        >

                            <Dialog.Panel
                                className="
                                    relative
                                    transform
                                    overflow-hidden
                                    md:min-w-lg
                                    md:max-w-xl
                                    rounded-lg
                                    border
                                    my-8
                                    p-6
                                    text-left
                                    shadow-xl
                                    transition-all
                                    border-gray-200
                                    dark:border-gray-800
                                    bg-white/90
                                    dark:bg-gray-900/90
                                    text-gray-900
                                    dark:text-white
                                "
                            >

                                <Dialog.Title
                                    as="h3"
                                    className="
                                        apg-references-modal-header
                                        font-semibold
                                        leading-6
                                        text-2xl
                                        mb-4
                                        text-center
                                        text-gray-900
                                        dark:text-white
                                    "
                                >
                                    {t('sources.title')}
                                </Dialog.Title>

                                <ul
                                    className="
                                        apg-references-modal-body
                                        list-disc
                                    "
                                >

                                    {sources && dedupeArray(sources, 'id').map(
                                        (source, index) =>
                                            (
                                                <li
                                                    key={index}
                                                    className="
                                                        apg-references-modal-item
                                                        block
                                                        my-6
                                                        text-gray-900
                                                        dark:text-white
                                                    "
                                                >

                                                    <a
                                                        className="flex gap-x-6"
                                                        href={getSourceReferralUrl(source, promptId, userId)}
                                                        target="_blank"
                                                        onClick={() => handleReferClick(source)}
                                                    >

                                                        {/*{source.image_url && (*/}
                                                        {/*    <div className="w-16 flex-none">*/}
                                                        {/*        <img*/}
                                                        {/*            src={source.image_url}*/}
                                                        {/*            alt={source.title}*/}
                                                        {/*            className="*/}
                                                        {/*                apg-references-modal-item-image*/}
                                                        {/*                w-full*/}
                                                        {/*                border*/}
                                                        {/*                border-gray-400*/}
                                                        {/*                dark:border-gray-600*/}
                                                        {/*                shadow-md*/}
                                                        {/*                dark:shadow-xl*/}
                                                        {/*            "*/}
                                                        {/*        />*/}
                                                        {/*    </div>*/}
                                                        {/*)}*/}

                                                        <div>

                                                            <h4
                                                                className="
                                                                    apg-references-modal-item-title
                                                                    text-xl
                                                                "
                                                            >
                                                                {source.title}
                                                            </h4>

                                                            <span
                                                                className="
                                                                    apg-references-modal-item-authors
                                                                    block
                                                                    text-md
                                                                    italic
                                                                    text-gray-500
                                                                "
                                                            >
                                                                {source.authors.join(', ')}
                                                            </span>

                                                            <button
                                                                className="
                                                                    apg-references-modal-item-cta
                                                                    inline-block
                                                                    bg-primary-500
                                                                    hover:bg-primary-600
                                                                    text-sm
                                                                    text-white
                                                                    rounded-3xl
                                                                    px-3
                                                                    py-2
                                                                    mt-2
                                                                "
                                                            >
                                                                {t('sources.more')}
                                                                <ArrowRightCircleIcon className="w-4 h-4 inline-block ltr:ml-1 rtl:mr-1" />
                                                            </button>

                                                        </div>

                                                    </a>

                                                </li>
                                            )
                                        )}
                                </ul>

                                <div className="mt-6 sm:mt-8 apg-references-modal-footer">
                                    <button
                                        type="button"
                                        className="
                                            apg-references-modal-close
                                            w-full
                                            rounded-full
                                            border
                                            px-2
                                            py-2
                                            text-md
                                            font-semibold
                                            focus-visible:outline
                                            focus-visible:outline-2
                                            focus-visible:outline-offset-2
                                            focus-visible:outline-primary-500
                                            border-gray-400
                                            dark:border-gray-600
                                            text-gray-400
                                            dark:text-gray-600
                                            hover:border-gray-600
                                            dark:hover:border-gray-400
                                            hover:text-gray-600
                                            dark:hover:text-gray-400
                                        "
                                        onClick={handleClose}
                                    >
                                        {t('navigation.close')}
                                    </button>
                                </div>

                            </Dialog.Panel>

                        </Transition.Child>

                    </div>
                </div>

            </Dialog>
        </Transition.Root>

    );

}
