import { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';
import {BookOpenIcon, ChevronDownIcon, LanguageIcon} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { track } from '@vercel/analytics';
import { usePathname, useSearchParams } from 'next/navigation';
import { redirectToUrl } from '../lib/request';

function classNames(...classes) {
    return classes.filter(Boolean).join(' ');
}

export default function TranslationSwitcher({ t, translation, supportedTranslations }) {

    const searchParams = useSearchParams();
    const pathname = usePathname();

    const handleTranslationOpen = () => {
        track('Translation:Open');
    };

    const handleTranslationClose = () => {
        track('Translation:Close');
    };

    const handleTranslationSwitch = function(newTranslation) {
        track('Translation:Switch', { from: translation, to: newTranslation });
        redirectToUrl(pathname, new URLSearchParams(searchParams), 'translation', newTranslation);
    };

    return (Object.keys(supportedTranslations).length > 1) && (
        <Menu
            id="apg-translation-switcher"
            as="div"
            className="relative"
        >

            <div>
                <Menu.Button
                    className="
                        p-2
                        rounded-lg
                        flex
                        items-center
                        justify-center
                        gap-x-2
                        shadow-sm
                        dark:shadow-xl
                        bg-gray-200
                        dark:bg-gray-800
                        text-gray-900
                        dark:text-white
                        hover:bg-gray-300
                        dark:hover:bg-gray-700
                        opacity-75
                        hover:opacity-100
                    "
                    title={supportedTranslations[translation]}
                    onClick={handleTranslationOpen}
                >
                    <BookOpenIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" aria-hidden="true" />
                    <span className="sr-only">{t('translations.label')}</span>
                    <span>{translation.toUpperCase()}</span>
                    <ChevronDownIcon className="h-4 w-4 text-gray-600 dark:text-gray-400 relative top-0.5" aria-hidden="true" />
                </Menu.Button>
            </div>

            <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
            >
                <Menu.Items
                    className="
                        absolute
                        ltr:right-0
                        rtl:left-0
                        z-10
                        mt-2
                        w-64
                        ltr:origin-top-right
                        rtl:origin-top-left
                        py-2
                        rounded-md
                        bg-white/95
                        dark:bg-gray-900/95
                        shadow-lg
                        ring-1
                        ring-white
                        dark:ring-black
                        ring-opacity-5
                        focus:outline-none
                        border
                        border-gray-100
                        dark:border-gray-800
                    "
                    onClose={handleTranslationClose}
                >
                    {Object.keys(supportedTranslations).filter(e => e !== translation).map((translationKey) => (
                        <Menu.Item key={translationKey}>
                            {({ active }) => (
                                <Link
                                    href="#"
                                    className={classNames(
                                        active ? 'bg-primary-500 text-white' : 'text-gray-800 dark:text-gray-300',
                                        'group flex items-center px-4 py-2 text-md'
                                    )}
                                    onClick={() => handleTranslationSwitch(translationKey)}
                                >
                                    {supportedTranslations[translationKey]}
                                </Link>
                            )}
                        </Menu.Item>
                    ))}
                </Menu.Items>
            </Transition>
        </Menu>
    );
}
