"use client";

import { VideoCameraIcon, MicrophoneIcon, DocumentIcon, ArrowRightCircleIcon } from '@heroicons/react/24/solid';
import { Fragment, useEffect, useRef, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { track } from '@vercel/analytics';
import { getReferralUrl, getSourceReferralUrl, trackImpression, trackView } from '../lib/tracking';

export default function MediaItem({
    t,
    item,
    promptId,
    userId,
}) {

    const [open, setOpen] = useState(false);

    const sourceReferralUrl = getSourceReferralUrl(item, promptId, userId);
    const primaryCollectionName = item.importer_collection_name ?? item.collection_names[0];
    const primaryCollectionReferralUrl = getReferralUrl(
        'collection',
        item.importer_collection_id,
        promptId,
        item.importer_collection_url ?? item.url,
        userId
    );

    let mediaImpression = useRef(false);
    useEffect(() => {
        if (promptId && !mediaImpression.hasRun) {
            trackImpression(item.id, promptId, userId);
            mediaImpression.hasRun = true;
        }
    }, [promptId]);

    const handleOpen = (e) => {
        track('Media:Open', {promptId: promptId, modelType: item.model, modelId: item.id});
        trackView(item.id, promptId, userId);
        const link = e.currentTarget;
        if (['youtube', 'episode'].includes(link.getAttribute('dataType'))) {
            setOpen(true);
        } else {
            window.open(link.getAttribute('href'), '_blank');
        }
        e.preventDefault();
        e.stopPropagation();
    };

    const handleClose = () => {
        setOpen(false);
        track('Media:Close', {promptId: promptId, modelType: item.model, modelId: item.id});
    };

    return (
        <>

            <a
                className={`
                    w-full
                    h-full
                    p-4
                    rounded-3xl
                    border
                    border-gray-200
                    dark:border-gray-800
                    bg-white/50
                    dark:bg-gray-900/50
                    shadow-md
                    dark:shadow-lg
                    opacity-75 
                    hover:opacity-100
                    cursor-pointer
                    flex
                    flex-col
                    gap-y-2
                    apg-media
                    apg-media-${item.type}
                `}
                href={sourceReferralUrl}
                target="_blank"
                onClick={handleOpen}
                datatype={item.type}
            >
                <div
                    className="
                        flex-none
                        relative
                        w-full
                        aspect-video
                        overflow-hidden
                        rounded-md
                        flex-col
                        items-center
                    "
                >
                    <img
                        src={item.image_url}
                        alt={item.title}
                        className="w-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                    />
                    <div className="absolute w-full h-full flex items-center justify-center top-0 left-0">
                        {(item.type === 'youtube') && (
                            <VideoCameraIcon className="w-16 h-16 text-white opacity-75" />
                        )}
                        {(item.type === 'episode') && (
                            <MicrophoneIcon className="w-16 h-16 text-white opacity-75" />
                        )}
                        {!['youtube', 'episode'].includes(item.type) && (
                            <DocumentIcon className="w-16 h-16 text-white opacity-75" />
                        )}
                    </div>
                </div>
                <h4 className="flex-grow font-semibold">
                    {item.title}
                </h4>
                <span className="flex-none block text-gray-600 dark:text-gray-400 italic">
                    {primaryCollectionName}
                </span>
            </a>

            <Transition.Root show={open} as={Fragment}>
                <Dialog
                    as="div"
                    className={`relative z-10 apg-media-modal`}
                    onClose={handleClose}
                >

                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div
                            className="
                                fixed
                                inset-0
                                bg-opacity-100
                                transition-opacity
                                bg-white/90
                                dark:bg-gray-900/90
                            "
                        />
                    </Transition.Child>

                    <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                        <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">

                            <Transition.Child
                                as={Fragment}
                                enter="ease-out duration-300"
                                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                enterTo="opacity-100 translate-y-0 sm:scale-100"
                                leave="ease-in duration-200"
                                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                            >

                                <Dialog.Panel
                                    className={`
                                        relative
                                        transform
                                        overflow-hidden
                                        w-full
                                        ${(item.type === 'youtube') ? 'md:w-1/2' : 'md:max-w-xl'}
                                        rounded-lg
                                        border
                                        my-8
                                        p-6
                                        text-left
                                        shadow-xl
                                        transition-all
                                        border-gray-200
                                        dark:border-gray-800
                                        bg-white/90
                                        dark:bg-gray-900/90
                                        text-gray-900
                                        dark:text-white
                                    `}
                                >

                                    <Dialog.Title
                                        as="h3"
                                        className="
                                            apg-media-modal-header
                                            font-semibold
                                            text-xl
                                            mb-4
                                            text-center
                                            text-gray-900
                                            dark:text-white
                                        "
                                    >
                                        <a href={sourceReferralUrl} target="_blank" className="hover:underline">{item.title}</a>
                                        {item.importer_collection_id && (
                                            <a href={primaryCollectionReferralUrl} target="_blank" className="mt-2 block text-gray-600 dark:text-gray-400 italic font-normal text-lg hover:underline">
                                                {primaryCollectionName}
                                            </a>
                                        )}
                                    </Dialog.Title>

                                    <div className="apg-media-modal-body">

                                        {(item.type === 'youtube') && (
                                            <div className="aspect-video">
                                                <iframe
                                                    className="w-full h-full"
                                                    src={`https://www.youtube.com/embed/${item.external_id}`}
                                                    title="YouTube video player"
                                                    frameBorder="0"
                                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                                    referrerPolicy="strict-origin-when-cross-origin"
                                                    allowFullScreen
                                                ></iframe>
                                            </div>
                                        )}

                                        {(item.type === 'episode') && (
                                            <div
                                                className="
                                                    flex-none
                                                    relative
                                                    w-full
                                                    md:w-2/3
                                                    rounded-xl
                                                    overflow-hidden
                                                    mx-auto
                                                "
                                            >
                                                <img
                                                    src={item.image_url}
                                                    alt={item.title}
                                                    className="w-full"
                                                />
                                                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent to-black opacity-50"></div>
                                                <audio controls className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                                    <source src={item.media_url} type="audio/mpeg" />
                                                    <a href={item.media_url} target="_blank" className="underline">{t('media.no_audio')}</a>
                                                </audio>
                                            </div>
                                        )}

                                    </div>


                                    <div className="mt-4 sm:mt-8 flex gap-6 apg-media-modal-footer">
                                        <button
                                            type="button"
                                            className="
                                                apg-media-modal-close
                                                w-full
                                                rounded-full
                                                border
                                                p-2
                                                text-md
                                                font-semibold
                                                focus-visible:outline
                                                focus-visible:outline-2
                                                focus-visible:outline-offset-2
                                                focus-visible:outline-primary-500
                                                border-gray-400
                                                dark:border-gray-600
                                                text-gray-400
                                                dark:text-gray-600
                                                hover:border-gray-600
                                                dark:hover:border-gray-400
                                                hover:text-gray-600
                                                dark:hover:text-gray-400
                                            "
                                            onClick={handleClose}
                                        >
                                            {t('navigation.close')}
                                        </button>
                                        <a
                                            href={sourceReferralUrl}
                                            target="_blank"
                                            className="
                                                apg-media-modal-cta
                                                w-full
                                                rounded-full
                                                p-2
                                                text-md
                                                font-semibold
                                                focus-visible:outline
                                                focus-visible:outline-2
                                                focus-visible:outline-offset-2
                                                inline-block
                                                bg-primary-500
                                                hover:bg-primary-600
                                                text-white
                                                text-center
                                            "
                                        >
                                            {t('sources.more')}
                                            <ArrowRightCircleIcon className="w-4 h-4 inline-block ltr:ml-1 rtl:mr-1" />
                                        </a>
                                    </div>

                                </Dialog.Panel>

                            </Transition.Child>

                        </div>
                    </div>

                </Dialog>
            </Transition.Root>

        </>
    );
};
