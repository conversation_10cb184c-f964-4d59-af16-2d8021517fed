"use client";

import Message from './Message';
import Cta from './Cta';
import { useEffect, useRef } from 'react';
import { debug } from '../lib/helpers';

export default function MessagesList({
    t,
    lng,
    messages,
    error,
    responding,
    streaming,
    agentId,
    userId,
    realTimeTranslation,
    responseFooterContent,
    responseFooterThreshold,
    disableAutoScroll,
    isEmbedded,
    showMedia,
    mediaCollectionIds,
    ctas,
}) {

    const bottomRef = useRef(null);

    const isCurrent = (i) => {
        return (i >= messages.length-2);
    };

    useEffect(() => {

        if (!disableAutoScroll && responding && (messages?.length > 1)) {
            bottomRef.current.scrollIntoView({behavior: 'smooth', block: 'start'});
        }
      }, [disableAutoScroll, messages?.length, responding]);


    const showResponseFooter = (
        responseFooterContent &&
        (
            !responseFooterThreshold ||
            (messages.length >= ((responseFooterThreshold-1) * 2))
        )
    );

    return (

        <div
            id="apg-messages-list"
            className="
                flex
                flex-col
                flex-auto
                relative
                overflow-hidden
                rounded-3xl
                border
                border-gray-200
                dark:border-gray-800
                bg-white/50
                dark:bg-gray-900/50
                shadow-sm
                dark:shadow-xl
            "
        >
            <div className="flex-auto overflow-y-auto !h-0">

                {(messages.length > 0) && (
                    <>

                        {messages.map((message, index) => (

                            <div key={index}>

                                {responding && isCurrent(index) && (
                                    <div ref={bottomRef} id="apg-message-current-prompt-ref" className="md:scroll-m-3" />
                                )}

                                <Message
                                    key={`message-${index}`}
                                    message={message}
                                    isCurrentPrompt={isCurrent(index)}
                                    responding={responding}
                                    agentId={agentId}
                                    t={t}
                                    lng={lng}
                                    userId={userId}
                                    realTimeTranslation={realTimeTranslation}
                                    isEmbedded={isEmbedded}
                                    showMedia={showMedia}
                                    mediaCollectionIds={mediaCollectionIds}
                                    ctas={ctas}
                                />

                            </div>

                        ))}

                        {responding && !streaming && (
                            <p className="apg-message-agent-loader px-4 md:px-6 pb-2 relative text-gray-700 dark:text-gray-300">
                                {t('prompt.thinking')}
                            </p>
                        )}

                        {error && (
                            <p className="apg-message-error relative px-4 py-2 text-red-500">
                                {error.text}
                            </p>
                        )}

                    </>
                )}

            </div>
        </div>

    );
};
