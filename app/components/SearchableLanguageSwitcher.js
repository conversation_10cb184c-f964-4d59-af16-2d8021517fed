import { Fragment, useState } from 'react';
import { Transition, Combobox, ComboboxInput, ComboboxOption, ComboboxOptions, ComboboxButton } from '@headlessui/react';
import { LanguageIcon, ChevronDownIcon, CheckIcon } from '@heroicons/react/24/solid';
import { track } from '@vercel/analytics';
import { useSearchParams } from 'next/navigation';
import { debug } from '../lib/helpers';
import { redirectToUrl } from '../lib/request';

export default function SearchableLanguageSwitcher({ lng, t, supportedLanguages, theme }) {

    const searchParams = useSearchParams();
    const languages = JSON.parse(process.env.NEXT_PUBLIC_AUTO_LANGUAGES);

    const handleLanguageOpen = () => {
        document.getElementById('apg-language-switcher-search').focus();
        track('Language:Open');
    };

    const handleLanguageClose = () => {
        setQuery('');
        track('Language:Close');
    };

    const handleLanguageSwitch = (language) => {
        if (language) {
            setSelectedLanguage(language);
            track('Language:Switch', { from: lng, to: language });
            redirectToUrl(language, new URLSearchParams(searchParams));
        }
    };

    const [selectedLanguage, setSelectedLanguage] = useState(lng);
    const [query, setQuery] = useState('');

    const filteredLanguages =
        query === ''
            ? supportedLanguages
            : supportedLanguages.filter((language) => {
                return languages[language].toLowerCase().includes(query.toLowerCase())
            });

    return (

        <Combobox
            immediate
            value={selectedLanguage}
            onChange={handleLanguageSwitch}
            onClose={handleLanguageClose}
            id="apg-language-switcher"
            as="div"
            className={`${theme} relative`}
        >

            <ComboboxButton
                as="div"
                className="
                    p-2
                    rounded-lg
                    flex
                    items-center
                    justify-center
                    gap-x-2
                    shadow-sm
                    dark:shadow-xl
                    bg-gray-200
                    dark:bg-gray-800
                    text-gray-900
                    dark:text-white
                    hover:bg-gray-300
                    dark:hover:bg-gray-700
                    opacity-75
                    hover:opacity-100
                    cursor-pointer
                "
                onClick={handleLanguageOpen}
            >
                <LanguageIcon className="h-6 w-6 text-gray-600 dark:text-gray-400" aria-hidden="true" />
                <span className="sr-only">{t('languages.label')}</span>
                <span>{languages[lng]}</span>
                <ChevronDownIcon className="h-4 w-4 text-gray-600 dark:text-gray-400 relative top-0.5" aria-hidden="true" />
            </ComboboxButton>

            <ComboboxInput
                id="apg-language-switcher-search"
                aria-label={t('languages.label')}
                displayValue={(language) => languages[language]}
                onChange={(event) => setQuery(event.target.value)}
                className="
                    invisible
                    data-[open]:visible
                    absolute
                    z-10
                    right-0
                    mt-2
                    w-48
                    px-4
                    rounded-3xl
                    border-0
                    ring-1
                    focus:ring-2
                    ring-inset
                    focus:ring-inset
                    focus:ring-primary-500
                    shadow-sm
                    dark:shadow-xl
                    bg-white/95
                    dark:bg-gray-900/95
                    text-gray-900
                    dark:text-white
                    ring-gray-300
                    dark:ring-gray-800
                    placeholder:text-gray-400
                    dark:placeholder:text-gray-600
                "
            />

            <Transition
                as={Fragment}
                enter="transition ease-out duration-100"
                enterFrom="transform opacity-0 scale-95"
                enterTo="transform opacity-100 scale-100"
                leave="transition ease-in duration-75"
                leaveFrom="transform opacity-100 scale-100"
                leaveTo="transform opacity-0 scale-95"
            >

                <ComboboxOptions
                    anchor="bottom"
                    transition
                    className={`
                        empty:invisible
                        mt-2
                        w-48
                        !max-h-96
                        overflow-y-auto
                        ltr:origin-top-right
                        rtl:origin-top-left
                        py-2
                        rounded-md
                        shadow-lg
                        ring-1
                        ring-opacity-5
                        focus:outline-none
                        border
                        text-gray-900
                        dark:text-white
                        bg-white/90
                        dark:bg-gray-900/90
                        ring-white
                        dark:ring-black
                        border-gray-100
                        dark:border-gray-800
                    `}
                >
                    {filteredLanguages.map((language) => (

                        <ComboboxOption
                            key={language}
                            value={language}
                            disabled={(language === selectedLanguage)}
                            className="
                                data-[focus]:bg-primary-500
                                data-[focus]:text-white
                                data-[disabled]:opacity-50
                                px-4
                                py-2
                                cursor-pointer
                                group
                            "
                        >
                            {languages[language]}
                        </ComboboxOption>

                    ))}
                </ComboboxOptions>

            </Transition>

        </Combobox>

    )

}


