"use client";

import { useState } from 'react';
import ProductSwitcher from './ProductSwitcher';
import LanguageSwitcher from './LanguageSwitcher';
import UserMenu from './UserMenu';
import TranslationSwitcher from './TranslationSwitcher';

export default function UserHeader({
    t,
    lng,
    translation,
    user,
    supportedLanguages,
    supportedTranslations,
}) {

    const [open, setOpen] = useState(false);

    return (
        <div className="mb-6">
            <nav className="flex h-16 px-6 ltr:pr-5 rtl:pl-5 shadow-md dark:shadow-xl ring-1 bg-white/50 dark:bg-gray-900/50 ring-black/10 dark:ring-white/10">

                <div className="flex-auto gap-x-4 lg:gap-x-6">
                    <ProductSwitcher lng={lng} t={t} center={false} />
                </div>

                <div className="ms-auto flex items-center gap-x-4">
                    <LanguageSwitcher lng={lng} t={t} supportedLanguages={supportedLanguages} />
                    <TranslationSwitcher t={t} translation={translation} supportedTranslations={supportedTranslations} />
                    <UserMenu lng={lng} t={t} user={user} />
                </div>

            </nav>
        </div>
    );
};
