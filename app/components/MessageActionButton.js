"use client";

const MessageActionButton = ({
    t,
    handleClick,
    btnClass,
    classes,
    Icon,
    ToggledIcon,
    toggled,
    cta,
    toggledMessage,
    iconClasses
}) => {

    return (

        <span className="tt apg-action-btn">
            <button
                type="button"
                onClick={handleClick}
                className={`
                    ${btnClass}
                    inline-flex
                    items-center
                    py-2
                    px-3
                    text-md
                    font-semibold
                    focus-visible:outline
                    focus-visible:outline-2
                    focus-visible:outline-offset-2
                    focus-visible:outline-primary-500
                    text-gray-600
                    dark:text-gray-400
                    hover:text-gray-900
                    dark:hover:text-white
                    hover:bg-gray-400
                    dark:hover:bg-gray-700
                    ${classes}
                `}
            >

                <span className="sr-only">{cta}</span>

                <Icon
                    className={`
                        ${btnClass}-off
                        w-6 
                        h-6 
                        ${iconClasses}
                        ${(toggled && ToggledIcon) ? 'hidden' : 'inline-block'}
                    `}
                />

                {ToggledIcon && (
                    <ToggledIcon
                        className={`
                        ${btnClass}-on
                        w-6 
                        h-6 
                        ${iconClasses}
                        ${toggled ? 'inline-block' : 'hidden'}
                    `}
                    />
                )}

            </button>

            <span
                className={`
                    tt-text
                    tt-pos-top
                    p-3
                    rounded-full
                    !w-48
                    !left-1/2
                    !-ml-24
                    !mt-4
                    bg-white/90
                    dark:bg-gray-900/90
                    shadow-md
                    dark:shadow-xl
                    text-gray-600
                    dark:text-gray-400
                    ${toggled ? '!hidden' : 'inline-block'}
                `}
            >{cta}</span>

            {toggledMessage && (
                <span
                    className={`
                        tt-text
                        tt-pos-top
                        p-3
                        rounded-full
                        !w-48
                        !left-1/2
                        !-ml-24
                        !mt-4
                        bg-white/90
                        dark:bg-gray-900/90
                        shadow-md
                        dark:shadow-xl
                        text-gray-600
                        dark:text-gray-400
                        ${toggled ? 'inline-block' : '!hidden'}
                    `}
                >{toggledMessage}</span>
            )}

        </span>

    );
};

export default MessageActionButton;
