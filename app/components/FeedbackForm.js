"use client"

import { useState} from 'react';
import { Transition, Field, Label, Input } from '@headlessui/react';
import { debug } from '../lib/helpers';
import { updatePrompt } from '../lib/request';
import { track } from '@vercel/analytics';
import TextareaAutosize from "react-textarea-autosize";

export default function FeedbackForm({
    lng,
    t,
    open,
    promptId,
    conversationId,
}) {

    const [feedback, setFeedback] = useState('');
    const [email, setEmail] = useState('');
    const [submitting, setSubmitting] = useState(false);
    const [submitted, setSubmitted] = useState(false);

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            submitFeedback(e);
        }
    };

    function submitFeedback(e) {
        e.preventDefault();
        if (feedback.trim().length > 0) {
            setSubmitting(true);
            track('Feedback:Submit', {promptId: promptId, feedback: feedback});
            updatePrompt(
                promptId,
                '/api/feedback',
                {
                    feedback: feedback,
                    conversation_id: conversationId
                }
            ).then(() => {
                setSubmitting(false);
                setSubmitted(true);
            });
        }
    }

    return (

        <Transition show={open}>

            <div
                className="
                    mx-auto
                    max-w-xl
                    mt-2
                    mb-4
                "
            >

                <h3 className="text-lg mb-4">{t('feedback.title')}</h3>

                <Field>
                    <Label className="sr-only">{t('feedback.label')}</Label>
                    <TextareaAutosize
                        autoFocus
                        className="
                            block
                            w-full
                            rounded-3xl
                            text-xl
                            border-0
                            ring-1
                            focus:ring-2
                            ring-inset
                            focus:ring-inset
                            focus:ring-primary-500
                            !px-4
                            !pt-[6px]
                            !pb-[7px]
                            shadow-sm
                            dark:shadow-xl
                            bg-white/50
                            dark:bg-black/25
                            text-gray-900
                            dark:text-white
                            ring-gray-300
                            dark:ring-gray-800
                            placeholder:text-gray-400
                            dark:placeholder:text-gray-600
                        "
                        placeholder={t('feedback.placeholder')}
                        required={true}
                        value={feedback}
                        minRows={4}
                        onChange={(e) => {
                            setFeedback(e.target.value);
                        }}
                        onKeyDown={handleKeyDown}
                        disabled={submitting}
                    />
                </Field>

                {/*<Field>*/}
                {/*    <Label className="sr-only">{t('feedback.email_label')}</Label>*/}
                {/*    <Input*/}
                {/*        type="email"*/}
                {/*        className="*/}
                {/*            block*/}
                {/*            w-full*/}
                {/*            rounded-3xl*/}
                {/*            text-xl*/}
                {/*            border-0*/}
                {/*            ring-1*/}
                {/*            focus:ring-2*/}
                {/*            ring-inset*/}
                {/*            focus:ring-inset*/}
                {/*            focus:ring-primary-500*/}
                {/*            !px-4*/}
                {/*            !pt-[6px]*/}
                {/*            !pb-[7px]*/}
                {/*            shadow-sm*/}
                {/*            dark:shadow-xl*/}
                {/*            bg-white/50*/}
                {/*            dark:bg-black/25*/}
                {/*            text-gray-900*/}
                {/*            dark:text-white*/}
                {/*            ring-gray-300*/}
                {/*            dark:ring-gray-800*/}
                {/*            placeholder:text-gray-400*/}
                {/*            dark:placeholder:text-gray-600*/}
                {/*        "*/}
                {/*        placeholder={t('feedback.email_hint')}*/}
                {/*        required={false}*/}
                {/*        value={email}*/}
                {/*        onChange={(e) => {*/}
                {/*            setEmail(e.target.value);*/}
                {/*        }}*/}
                {/*        onKeyDown={handleKeyDown}*/}
                {/*        disabled={submitting}*/}
                {/*    />*/}
                {/*</Field>*/}

                <div className="flex justify-end mt-5 gap-x-8">
                    <span
                        className={`
                            w-1/2
                            py-2
                            text-green-600
                            dark:text-green-500
                            ${submitted ? 'inline-block' : 'hidden'}
                        `}
                    >
                        {t('feedback.success')}
                    </span>
                    <button
                        onClick={submitFeedback}
                        className={`
                            w-1/2
                            rounded-full
                            px-2
                            py-2
                            text-md
                            font-semibold
                            focus-visible:transparent
                            bg-primary-500
                            hover:bg-primary-600
                            text-white
                            focus-visible:outline
                            focus-visible:outline-2
                            focus-visible:outline-offset-2
                            focus-visible:outline-primary-500
                            shadow-md
                            dark:shadow-xl
                        `}
                        disabled={submitting}
                    >
                        <svg
                            className={`
                                animate-spin 
                                ltr:-ml-1 
                                ltr:mr-3
                                rtl:-mr-1 
                                rtl:ml-3
                                h-5 
                                w-5 
                                text-white
                                ${submitting ? 'inline-block' : 'hidden'}
                            `}
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                        >
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {t('feedback.submit')}
                    </button>
                </div>

            </div>

        </Transition>

    );

}
