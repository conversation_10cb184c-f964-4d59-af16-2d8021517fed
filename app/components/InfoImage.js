import BeaconImgDark from '../../public/assets/images/apologist-beacon-dark.svg';
import AgentImgDark from '../../public/assets/images/apologist-agent-dark.svg';
import BeaconImgLight from '../../public/assets/images/apologist-beacon-light.svg';
import AgentImgLight from '../../public/assets/images/apologist-agent-light.svg';
import InfoModal from './InfoModal';
import { useState } from "react";
import { track } from "@vercel/analytics";

export default function InfoImage({
    t,
    isEmbedded,
    theme,
    agentCreatorName,
    agentImage,
    agentDescription,
    agentCreatorUrl,
    isOpenSource,
}) {

    const [open, setOpen] = useState(false);

    const handleInfoOpen = () => {
        setOpen(true);
        track('Info:Open');
    };

    return (
        <>

            <button
                id="apg-info-image"
                type="button"
                className="
                    opacity-50
                    hover:opacity-100
                    mx-auto
                "
                onClick={handleInfoOpen}
            >

                <span className="sr-only">{t('about.nav')}</span>

                {agentImage ? (
                    <img
                        src={agentImage}
                        alt={agentCreatorName ?? t('about.title')}
                        className="h-12 w-auto"
                    />
                ) : (
                    <>
                        {isEmbedded ? (
                            <BeaconImgDark className="h-12 w-auto flex hidden dark:block text-primary-500"
                                           title={t('app')}/>
                        ) : (
                            <AgentImgDark className="h-12 w-auto flex hidden dark:block text-primary-500"
                                          title={t('app')}/>
                        )}
                        {isEmbedded ? (
                            <BeaconImgLight className="h-12 w-auto flex dark:hidden text-primary" title={t('app')}/>
                        ) : (
                            <AgentImgLight className="h-12 w-auto flex dark:hidden text-primary" title={t('app')}/>
                        )}
                    </>
                )}

            </button>

            <InfoModal
                t={t}
                open={open}
                setOpen={setOpen}
                theme={theme}
                agentCreatorName={agentCreatorName}
                agentImage={agentImage}
                agentDescription={agentDescription}
                agentCreatorUrl={agentCreatorUrl}
                isOpenSource={isOpenSource}
            />

        </>

    )
}


