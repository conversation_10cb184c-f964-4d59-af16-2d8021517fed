"use client";

import {
    useState,
    useEffect,
} from 'react';
import {
    BookOpenIcon,
    ClipboardDocumentIcon,
    ClipboardDocumentCheckIcon,
    ShareIcon,
    CheckCircleIcon,
    HandThumbUpIcon,
    FlagIcon,
} from '@heroicons/react/20/solid';
import ReferencesModal from './ReferencesModal';
import FeedbackForm from './FeedbackForm';
import { track } from '@vercel/analytics';
import {
    getConversationId,
    getShareUrl,
    debug,
} from '../lib/helpers';
import { updatePrompt } from '../lib/request';
import * as copyToClipboard from 'copy-to-clipboard';
import * as removeMarkdown from 'remove-markdown';
import MessageActionButton from './MessageActionButton';
import { trackView } from '../lib/tracking';

const MessageActionsBar = ({
    t,
    message,
    messageText,
    isCurrentPrompt,
    responding,
    agentId,
    userId,
    theme,
    isEmbedded,
}) => {

    const [isClient, setIsClient] = useState(false);
    const [sourcesOpen, setSourcesOpen] = useState(false);
    const [copied, setCopied] = useState(false);
    const [shared, setShared] = useState(false);
    const [liked, setLiked] = useState(null);
    const [flagged, setFlagged] = useState(null);

    const isShared = message?.metadata?.metadata?.conversation ?
        (message.metadata?.metadata.conversation !== getConversationId()) :
        false
    ;

    useEffect(() => {
        setIsClient(true)
    }, []);

    const handleCopy = (e) => {
        track('Response:Copy', {promptId: message.metadata?.id});
        copyToClipboard(message.metadata?.prompt + "\n\n---\n\n" + removeMarkdown(messageText));
        setCopied(true);
        setTimeout(() => setCopied(false), 5000)
    };

    const handleShare = (e) => {
        track('Response:Share', {promptId: message.metadata?.id});
        const shareUrl = getShareUrl(agentId, message.metadata?.metadata.conversation, message.metadata?.id);
        copyToClipboard(shareUrl);
        setShared(true);
        setTimeout(() => setShared(false), 5000)
    };

    const handleSources = (e) => {
        track('Response:ViewSources', {promptId: message.metadata?.id});
        setSourcesOpen(true);
        for (const source of message.metadata?.sources) {
            trackView(source.id, message.metadata?.id, userId);
        }
    };

    const handleLike = (e) => {
        setLiked(!liked);
    };
    useEffect(() => {
        if (liked !== null) {
            track('Response:' + (liked ? 'Like' : 'Unlike'), {promptId: message.metadata?.id, liked: liked});
            updatePrompt(
                message.metadata?.id,
                '/api/like',
                {
                    liked: liked,
                    conversation_id: message.metadata?.metadata.conversation
                }
            );
        }
    }, [liked, message]);

    const handleFlag = (e) => {
        setFlagged(!flagged);
    };
    useEffect(() => {
        if (flagged !== null) {
            track('Response:' + (flagged ? 'Flag' : 'Unflag'), {promptId: message.metadata?.id, flagged: flagged});
            updatePrompt(
                message.metadata?.id,
                '/api/flag',
                {
                    flagged: flagged,
                    conversation_id: message.metadata?.metadata.conversation
                }
            );
        }
    }, [flagged, message]);

    const hasSources = message.metadata?.sources && (message.metadata?.sources.length > 0);

    return (
        <>

            {isClient &&(!isCurrentPrompt || !responding) && (

                <div className="my-8 text-center">

                    <span
                        className="
                            apg-message-actions
                            mx-auto
                            isolate
                            inline-flex
                            w-auto
                            items-center
                            justify-center
                            rounded-full
                            bg-gray-200
                            dark:bg-gray-800
                        "
                    >

                        <MessageActionButton
                            handleClick={handleCopy}
                            btnClass="apg-copy-btn"
                            Icon={ClipboardDocumentIcon}
                            ToggledIcon={ClipboardDocumentCheckIcon}
                            toggled={copied}
                            cta={t('copy.cta')}
                            toggledMessage={t('copy.success')}
                        />

                        {!isShared && (
                            <MessageActionButton
                                handleClick={handleLike}
                                btnClass="apg-like-btn"
                                classes={liked ? 'text-primary-500' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}
                                Icon={HandThumbUpIcon}
                                ToggledIcon={false}
                                toggled={liked}
                                cta={t('like.cta')}
                                toggledMessage={t('like.success')}
                            />
                        )}

                        {hasSources && (
                            <MessageActionButton
                                handleClick={handleSources}
                                btnClass="apg-references-btn"
                                classes="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                                Icon={BookOpenIcon}
                                ToggledIcon={false}
                                toggled={false}
                                cta={t('sources.cta')}
                            />
                        )}

                        {!isShared && (
                            <MessageActionButton
                                handleClick={handleFlag}
                                btnClass="apg-flag-btn"
                                classes={flagged ? 'text-primary-500' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'}
                                Icon={FlagIcon}
                                ToggledIcon={false}
                                toggled={flagged}
                                cta={t('flag.cta')}
                                iconClasses="!w-5"
                            />
                        )}

                        {!isEmbedded && (
                            <MessageActionButton
                                handleClick={handleShare}
                                btnClass="apg-share-btn"
                                Icon={ShareIcon}
                                ToggledIcon={CheckCircleIcon}
                                toggled={shared}
                                cta={t('share.cta')}
                                toggledMessage={t('share.success')}
                            />
                        )}

                    </span>

                </div>

            )}

            {!isShared && (
                <FeedbackForm
                    open={flagged}
                    promptId={message.metadata?.id}
                    t={t}
                    conversationId={message.metadata?.metadata.conversation}
                />
            )}

            <ReferencesModal
                open={sourcesOpen}
                setOpen={setSourcesOpen}
                sources={message.metadata?.sources}
                t={t}
                userId={userId}
                theme={theme}
                promptId={message.metadata?.id}
            />

        </>
    );
};

export default MessageActionsBar;
