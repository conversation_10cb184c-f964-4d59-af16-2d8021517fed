{"app": "Apologist.ai", "sponsor": "Uka Proyecto Disculpador ukax mä jach’a uñacht’äwiwa", "navigation": {"close": "Jist&#39;anta<PERSON>"}, "footer": {"text": "<span class=\"hidden sm:inline\"><PERSON><PERSON> inaki yanapt’awix</span> chiqa chuyman yanapt’irinakan luratawa.", "cta": "Jichhax <PERSON>"}, "prompt": {"label": "Mä Prompt ukar man<PERSON>wa", "placeholder": "Aka chiqan mä jiskt’a jiskt’a<PERSON><PERSON>", "submit": "Uñ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Prompt", "dictate": "Dictar ukax mä juk’a pach<PERSON>kanwa", "reset": "<PERSON>hana <PERSON>awi", "error": "Yatiyawimax wali jach’awa. Mä jisk’a yatiyawimpiw mayamp yant’añama.", "wait": "U<PERSON><PERSON><PERSON> suyt&#39;apxam ...", "thinking": "Amuyt’aña"}, "response": {"show": "Uñacht<PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON><PERSON>"}, "media": {"title": "Uñt’at Medios de Comunicación ukanakampi", "no_audio": "Aka tuqir ch’iqt’am ist’añataki"}, "copy": {"cta": "Clipboard ukar copiaña", "success": "¡Respuesta ukax copiatawa!"}, "sources": {"cta": "Uñakipt’añataki <PERSON>", "title": "<PERSON><PERSON>hiwinakan apnaqat jamuqanaka", "more": "Juk’amp ya<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "like": {"cta": "<PERSON><PERSON><PERSON>", "success": "¡Respuesta ukax wali askiwa!"}, "flag": {"cta": "Mä <PERSON> ukar yatiya<PERSON>", "success": "¡Issue yatiyatawa!"}, "feedback": {"title": "Mä <PERSON> ukar yatiya<PERSON>", "placeholder": "Jiwasax juk’amp ask<PERSON>k lura<PERSON> ya<PERSON>’añatakix amuyt’awinak chura<PERSON>.", "email_label": "Ukax Correo electrónico ukan u<PERSON> (jani<PERSON>) .", "email_hint": "<PERSON><PERSON><PERSON> wakis<PERSON>wa, jumatix yatiyapkta uka jan walt’äwit jumamp jikisiñ munsta ukhakiw wakisi.", "submit": "Ukax mä juk’a pach<PERSON>kanwa", "success": "¡Amuyunakax uñt’ayatawa!"}, "share": {"cta": "Copia Compartir Link ukax mä juk’a pach<PERSON>kanwa", "success": "¡Compartir enlace ukax copiatawa!"}, "about": {"nav": "Ukxata", "title": "Uka Proyecto Disculpador ukax mä jach’a uñacht’äwiwa", "intro1": "Jiwasan tecnología generativa AI ukax cristianismo tuqit jan walt’a<PERSON><PERSON><PERSON><PERSON> j<PERSON>, mä LLM (Modelo de Lengua Jach’a) uka apnaqasa, ukax apologista clásica ukhamarak contemporánea ukanakan qillqatanakapat yatichatawa. Mä juk’a <PERSON> Aquino ukamp aruskipt’añjamawa.", "intro2": "Proyecto Apologista ukax voluntarios ukat qullqi tuqit yanapt’irinakasan jach’a qullqi churatanakapamp luratawa. Uraqpachan chiqa yatich<PERSON>wi thaq<PERSON><PERSON>kar puriñatakikix tiempomsa jan ukax yänakamsa churañatakix irpataw jikxatasïta ukhaxa, jawstʼasiñamawa.", "cta": "Juk’amp ya<PERSON><PERSON><PERSON><PERSON><PERSON>", "open": "Jisk’a jamuqanaka"}, "contact": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "description": "Código QR ukax dispositivo móvil ukampiw escanear, jan ukax aka tuqir qillqt’apxita: "}, "languages": {"label": "Aru:"}, "translations": {"label": "Biblia Jaqukipaña:"}, "questions": {"title": "Akax mä qawqha uñacht’äw jiskt’äwinakawa yant’añataki:"}}