{"app": "Apologist.ai", "sponsor": "O Projeto Apologista", "navigation": {"close": "<PERSON><PERSON><PERSON>"}, "footer": {"text": "<span class=\"hidden sm:inline\">Este serviço gratuito é</span> possível graças a doadores fiéis.", "cta": "Doe agora"}, "prompt": {"label": "Insira um prompt", "placeholder": "Faça uma pergunta aqui", "submit": "Enviar Prompt", "dictate": "Ditar Prompt", "reset": "Limpar Prompt", "error": "Sua mensagem é muito longa. Por favor, tente novamente com uma mensagem mais curta.", "wait": "Por favor, aguarde ...", "thinking": "Pensamento"}, "response": {"show": "<PERSON>rar resposta", "hide": "Ocultar resposta"}, "media": {"title": "Mídia relacionada", "no_audio": "Clique aqui para ouvir"}, "copy": {"cta": "Copiar para a área de transferência", "success": "Resposta copiada!"}, "sources": {"cta": "Ver fontes", "title": "Fontes usadas nesta resposta", "more": "<PERSON>ber mais"}, "like": {"cta": "Como", "success": "Resposta curtida!"}, "flag": {"cta": "Relatar um problema", "success": "Problema relatado!"}, "feedback": {"title": "Relatar um problema", "placeholder": "Por favor, envie seu feedback para nos ajudar a melhorar.", "email_label": "Seu endereço de e-mail (opcional)", "email_hint": "Necess<PERSON><PERSON> somente se você desejar que entremos em contato com você sobre o problema relatado.", "submit": "Enviar feedback", "success": "Feedback enviado!"}, "share": {"cta": "Copiar Compartilhar Link", "success": "Compartilhar link copiado!"}, "about": {"nav": "Sobre", "title": "O Projeto Apologista", "intro1": "Nossa tecnologia de IA generativa responde a objeções comuns ao cristianismo usando um LLM (Large Language Model) treinado em obras de autoria de apologistas cristãos clássicos e contemporâneos. É como conversar com Tomás de Aquino.", "intro2": "O Apologist Project é possível graças a voluntários e às generosas doações de nossos apoiadores financeiros. Entre em contato se você se sentir levado a contribuir com seu tempo ou recursos para alcançar buscadores da verdade em todo o mundo.", "cta": "<PERSON>ber mais", "open": "<PERSON><PERSON><PERSON> a<PERSON>"}, "contact": {"title": "Continue a conversa.", "description": "Escaneie o código QR com seu dispositivo móvel ou envie uma mensagem para: "}, "languages": {"label": "Linguagem:"}, "translations": {"label": "Tradução da Bíblia:"}, "questions": {"title": "Aqui estão alguns exemplos de perguntas para você tentar:"}}