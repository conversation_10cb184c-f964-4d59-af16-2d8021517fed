{"app": "Apologist.ai", "sponsor": "Proyecto Disculpador nisqa", "navigation": {"close": "<PERSON><PERSON><PERSON><PERSON>"}, "footer": {"text": "<span class=\"hidden sm:inline\"><PERSON> mana qullqi<PERSON><PERSON>q yanap<PERSON></span> hunt’aq sunqu qullqi quqkunam ruwanku.", "cta": "<PERSON><PERSON>"}, "prompt": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON> ruway", "submit": "Enviar Prompta nisqa", "dictate": "Dictar Prompta nisqa", "reset": "Ch&#39;<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ancha un<PERSON>. <PERSON>a hina <PERSON>, aswan pisi willakuywan huk<PERSON> ka<PERSON>.", "wait": "<PERSON>a hina ka<PERSON>, su<PERSON><PERSON><PERSON> ...", "thinking": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "response": {"show": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON><PERSON><PERSON> pakay"}, "media": {"title": "Relacionadas Medios de Comunicación", "no_audio": "Kaypi <PERSON>"}, "copy": {"cta": "Clipboard nisqaman copiay", "success": "¡<PERSON>tic<PERSON>y copiasqa!"}, "sources": {"cta": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> qhaway", "title": "<PERSON>achi<PERSON> pukyuta", "more": "<PERSON><PERSON><PERSON>"}, "like": {"cta": "Munasqa", "success": "¡Kutichiy gustarqa!"}, "flag": {"cta": "<PERSON><PERSON><PERSON>", "success": "¡<PERSON><PERSON> willas<PERSON>!"}, "feedback": {"title": "<PERSON><PERSON><PERSON>", "placeholder": "<PERSON>a hina ka<PERSON>a, allinchaypaq yanapawanay<PERSON>paq yuyaykunata quy.", "email_label": "Correo electr<PERSON><PERSON> (munasqa) .", "email_hint": "Necesariolla sichus munanki will<PERSON>i sa<PERSON>.", "submit": "Enviar Retroalimentación nisqa", "success": "¡Retroalimentación enviada!"}, "share": {"cta": "Copia Compartir <PERSON>", "success": "¡Compartir enlace copiado!"}, "about": {"nav": "Sobre", "title": "Proyecto Disculpador nisqa", "intro1": "Tecnología generativa AI nisqaykuqa kutichin común hark&#39;akuykunata cristiano kayman huk LLM (Modelo de Lenguaje Hatun) llamk&#39;a<PERSON><PERSON><PERSON>, yachachisqa llamk&#39;<PERSON><PERSON><PERSON><PERSON>, is<PERSON>ynin clásico hinallataq kunan pacha cristiano disculpaqkunap qillqasqan. Chayqa huk laya <PERSON> rim<PERSON> hina.", "intro2": "Proyecto Disculpador nisqataqa ruwakun voluntarios nis<PERSON><PERSON><PERSON><PERSON>, qolqe yanapawaqninchiskunaq tukuy sonqowan qosqankuwan ima. Rimanakuy sichus pusasqa hina sientekunki tiempoykita otaq kapuqniykita qonaykipaq, chhaynapi teqsimuyuntinpi cheqaq kaq maskhaqkunaman chayanaykipaq.", "cta": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON>"}, "contact": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON> ruway.", "description": "Dispositivoykiwan QR códigota escaneay, otaq kayman a<PERSON>mu<PERSON>: "}, "languages": {"label": "<PERSON><PERSON>:"}, "translations": {"label": "Bibliaq t’ikrakuynin:"}, "questions": {"title": "<PERSON><PERSON> wakin ejemplo tap<PERSON>ta pruebanaykipaq:"}}