{"app": "Apologist.ai", "sponsor": "The Apologist Project", "navigation": {"close": "Close"}, "footer": {"text": "<span class=\"hidden sm:inline\">This free service is</span> made possible by faithful donors.", "cta": "Give Now"}, "prompt": {"label": "Enter a Prompt", "placeholder": "Ask a question here", "submit": "Submit Prompt", "dictate": "Dictate Prompt", "reset": "Clear Prompt", "error": "Your message is too long. Please try again with a shorter message.", "wait": "Please wait ...", "thinking": "Thinking"}, "response": {"show": "Show Response", "hide": "Hide Response"}, "media": {"title": "Related Media", "no_audio": "Click here to listen"}, "copy": {"cta": "Copy to Clipboard", "success": "Response copied!"}, "sources": {"cta": "View Sources", "title": "Sources Used in this Response", "more": "Learn More"}, "like": {"cta": "Like", "success": "Response liked!"}, "flag": {"cta": "Report an Issue", "success": "Issue reported!"}, "feedback": {"title": "Report an Issue", "placeholder": "Please provide feedback to help us improve.", "email_label": "Your Email Address (optional)", "email_hint": "Only necessary if you wish us to contact you about the issue you reported.", "submit": "Submit <PERSON>", "success": "<PERSON><PERSON><PERSON> submitted!"}, "share": {"cta": "Copy Share Link", "success": "Share link copied!"}, "about": {"nav": "About", "title": "The Apologist Project", "intro1": "Our generative AI technology answers common objections to Christianity by using an LLM (Large Language Model) trained on works authored by both classical and contemporary Christian apologists. It's kind of like chatting with <PERSON>.", "intro2": "The Apologist Project is made possible by volunteers and the generous donations of our financial supporters. Get in touch if you feel led to contribute your time or resources to reach seekers of truth worldwide.", "cta": "Learn More", "open": "Open Source"}, "contact": {"title": "Continue the conversation.", "description": "Scan the QR code with your mobile device, or text us at: "}, "languages": {"label": "Language:"}, "translations": {"label": "Bible Translation:"}, "questions": {"title": "Here are some example questions to try:"}}