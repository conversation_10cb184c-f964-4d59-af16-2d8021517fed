export const fallbackLng = process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE;
export const languages = Object.keys(JSON.parse(process.env.NEXT_PUBLIC_AUTO_LANGUAGES));
export const defaultNS = 'translation'
export const cookieName = 'i18next'

export function getOptions (lng = fallbackLng, ns = defaultNS) {
    return {
        // debug: true,
        supportedLngs: languages,
        fallbackLng,
        lng,
        fallbackNS: defaultNS,
        defaultNS,
        ns
    }
}
