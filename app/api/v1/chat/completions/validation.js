"use strict";module.exports = validate20;module.exports.default = validate20;const schema22 = {"type":"object","properties":{"audio":{"type":"object","nullable":true},"frequency_penalty":{"type":"number","nullable":true},"logit_bias":{"type":"object","nullable":true},"logprobs":{"type":"object","nullable":true},"max_completion_tokens":{"type":"integer","nullable":true},"max_tokens":{"type":"integer","nullable":true},"messages":{"type":"array","nullable":true},"metadata":{"type":"object","nullable":true,"properties":{"anonymous":{"type":"boolean","nullable":false},"conversation":{"type":"string","nullable":true,"minLength":1,"maxLength":100},"max_memories":{"type":"integer","nullable":true},"language":{"type":"string","nullable":true,"minLength":2,"maxLength":10},"parent_url":{"type":"string","nullable":true,"minLength":5,"maxLength":1000},"parent_host":{"type":"string","nullable":true,"minLength":5,"maxLength":100},"session":{"type":"string","nullable":true,"minLength":1,"maxLength":100},"device":{"type":"string","nullable":true,"minLength":1,"maxLength":100},"shared_prompt":{"type":"integer","nullable":true},"translation":{"type":"string","nullable":true,"minLength":3,"maxLength":4}}},"modalities":{"type":"array","nullable":true},"model":{"type":"string","nullable":true,"minLength":1,"maxLength":100},"n":{"type":"integer","nullable":true},"parallel_tool_calls":{"type":"boolean","nullable":true},"prediction":{"type":"object","nullable":true},"presence_penalty":{"type":"number","nullable":true},"prompt":{"type":"string","nullable":true,"minLength":2,"maxLength":100000},"reasoning_effort":{"type":"string","nullable":true,"enum":["low","medium","high"]},"verbosity":{"type":"string","nullable":true,"enum":["minimal","low","medium","high"]},"response_format":{"type":"object","nullable":true,"properties":{"type":{"type":"string","nullable":true,"enum":["text","html","json","raw"]}}},"seed":{"type":"integer","nullable":true},"service_tier":{"type":"string","nullable":true},"stop":{"type":"string","nullable":true},"store":{"type":"boolean","nullable":true},"stream":{"type":"boolean","nullable":false},"stream_options":{"type":"object","nullable":true},"temperature":{"type":"number","nullable":true},"top_p":{"type":"number","nullable":true},"tools":{"type":"array","nullable":true},"tool_choice":{"type":"object","nullable":true},"user":{"type":"string","nullable":true,"minLength":1,"maxLength":100}},"required":[],"additionalProperties":false,"oneOf":[{"required":["prompt"]},{"required":["messages"]}]};const func4 = Object.prototype.hasOwnProperty;const func5 = require("ajv/dist/runtime/ucs2length").default;function validate20(data, {instancePath="", parentData, parentDataProperty, rootData=data}={}){let vErrors = null;let errors = 0;const _errs1 = errors;let valid0 = false;let passing0 = null;const _errs2 = errors;if(data && typeof data == "object" && !Array.isArray(data)){let missing0;if((data.prompt === undefined) && (missing0 = "prompt")){const err0 = {instancePath,schemaPath:"#/oneOf/0/required",keyword:"required",params:{missingProperty: missing0},message:"must have required property '"+missing0+"'"};if(vErrors === null){vErrors = [err0];}else {vErrors.push(err0);}errors++;}}var _valid0 = _errs2 === errors;if(_valid0){valid0 = true;passing0 = 0;}const _errs3 = errors;if(data && typeof data == "object" && !Array.isArray(data)){let missing1;if((data.messages === undefined) && (missing1 = "messages")){const err1 = {instancePath,schemaPath:"#/oneOf/1/required",keyword:"required",params:{missingProperty: missing1},message:"must have required property '"+missing1+"'"};if(vErrors === null){vErrors = [err1];}else {vErrors.push(err1);}errors++;}}var _valid0 = _errs3 === errors;if(_valid0 && valid0){valid0 = false;passing0 = [passing0, 1];}else {if(_valid0){valid0 = true;passing0 = 1;}}if(!valid0){const err2 = {instancePath,schemaPath:"#/oneOf",keyword:"oneOf",params:{passingSchemas: passing0},message:"must match exactly one schema in oneOf"};if(vErrors === null){vErrors = [err2];}else {vErrors.push(err2);}errors++;validate20.errors = vErrors;return false;}else {errors = _errs1;if(vErrors !== null){if(_errs1){vErrors.length = _errs1;}else {vErrors = null;}}}if(errors === 0){if(data && typeof data == "object" && !Array.isArray(data)){const _errs4 = errors;for(const key0 in data){if(!(func4.call(schema22.properties, key0))){validate20.errors = [{instancePath,schemaPath:"#/additionalProperties",keyword:"additionalProperties",params:{additionalProperty: key0},message:"must NOT have additional properties"}];return false;break;}}if(_errs4 === errors){if(data.audio !== undefined){let data0 = data.audio;const _errs5 = errors;if((!(data0 && typeof data0 == "object" && !Array.isArray(data0))) && (data0 !== null)){validate20.errors = [{instancePath:instancePath+"/audio",schemaPath:"#/properties/audio/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}var valid1 = _errs5 === errors;}else {var valid1 = true;}if(valid1){if(data.frequency_penalty !== undefined){let data1 = data.frequency_penalty;const _errs8 = errors;if((!((typeof data1 == "number") && (isFinite(data1)))) && (data1 !== null)){validate20.errors = [{instancePath:instancePath+"/frequency_penalty",schemaPath:"#/properties/frequency_penalty/type",keyword:"type",params:{type: "number"},message:"must be number"}];return false;}var valid1 = _errs8 === errors;}else {var valid1 = true;}if(valid1){if(data.logit_bias !== undefined){let data2 = data.logit_bias;const _errs11 = errors;if((!(data2 && typeof data2 == "object" && !Array.isArray(data2))) && (data2 !== null)){validate20.errors = [{instancePath:instancePath+"/logit_bias",schemaPath:"#/properties/logit_bias/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}var valid1 = _errs11 === errors;}else {var valid1 = true;}if(valid1){if(data.logprobs !== undefined){let data3 = data.logprobs;const _errs14 = errors;if((!(data3 && typeof data3 == "object" && !Array.isArray(data3))) && (data3 !== null)){validate20.errors = [{instancePath:instancePath+"/logprobs",schemaPath:"#/properties/logprobs/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}var valid1 = _errs14 === errors;}else {var valid1 = true;}if(valid1){if(data.max_completion_tokens !== undefined){let data4 = data.max_completion_tokens;const _errs17 = errors;if((!(((typeof data4 == "number") && (!(data4 % 1) && !isNaN(data4))) && (isFinite(data4)))) && (data4 !== null)){validate20.errors = [{instancePath:instancePath+"/max_completion_tokens",schemaPath:"#/properties/max_completion_tokens/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs17 === errors;}else {var valid1 = true;}if(valid1){if(data.max_tokens !== undefined){let data5 = data.max_tokens;const _errs20 = errors;if((!(((typeof data5 == "number") && (!(data5 % 1) && !isNaN(data5))) && (isFinite(data5)))) && (data5 !== null)){validate20.errors = [{instancePath:instancePath+"/max_tokens",schemaPath:"#/properties/max_tokens/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs20 === errors;}else {var valid1 = true;}if(valid1){if(data.messages !== undefined){let data6 = data.messages;const _errs23 = errors;if((!(Array.isArray(data6))) && (data6 !== null)){validate20.errors = [{instancePath:instancePath+"/messages",schemaPath:"#/properties/messages/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}var valid1 = _errs23 === errors;}else {var valid1 = true;}if(valid1){if(data.metadata !== undefined){let data7 = data.metadata;const _errs26 = errors;if((!(data7 && typeof data7 == "object" && !Array.isArray(data7))) && (data7 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata",schemaPath:"#/properties/metadata/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}if(errors === _errs26){if(data7 && typeof data7 == "object" && !Array.isArray(data7)){if(data7.anonymous !== undefined){const _errs29 = errors;if(typeof data7.anonymous !== "boolean"){validate20.errors = [{instancePath:instancePath+"/metadata/anonymous",schemaPath:"#/properties/metadata/properties/anonymous/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];return false;}var valid2 = _errs29 === errors;}else {var valid2 = true;}if(valid2){if(data7.conversation !== undefined){let data9 = data7.conversation;const _errs32 = errors;if((typeof data9 !== "string") && (data9 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata/conversation",schemaPath:"#/properties/metadata/properties/conversation/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs32){if(typeof data9 === "string"){if(func5(data9) > 100){validate20.errors = [{instancePath:instancePath+"/metadata/conversation",schemaPath:"#/properties/metadata/properties/conversation/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data9) < 1){validate20.errors = [{instancePath:instancePath+"/metadata/conversation",schemaPath:"#/properties/metadata/properties/conversation/minLength",keyword:"minLength",params:{limit: 1},message:"must NOT have fewer than 1 characters"}];return false;}}}}var valid2 = _errs32 === errors;}else {var valid2 = true;}if(valid2){if(data7.max_memories !== undefined){let data10 = data7.max_memories;const _errs35 = errors;if((!(((typeof data10 == "number") && (!(data10 % 1) && !isNaN(data10))) && (isFinite(data10)))) && (data10 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata/max_memories",schemaPath:"#/properties/metadata/properties/max_memories/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid2 = _errs35 === errors;}else {var valid2 = true;}if(valid2){if(data7.language !== undefined){let data11 = data7.language;const _errs38 = errors;if((typeof data11 !== "string") && (data11 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata/language",schemaPath:"#/properties/metadata/properties/language/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs38){if(typeof data11 === "string"){if(func5(data11) > 10){validate20.errors = [{instancePath:instancePath+"/metadata/language",schemaPath:"#/properties/metadata/properties/language/maxLength",keyword:"maxLength",params:{limit: 10},message:"must NOT have more than 10 characters"}];return false;}else {if(func5(data11) < 2){validate20.errors = [{instancePath:instancePath+"/metadata/language",schemaPath:"#/properties/metadata/properties/language/minLength",keyword:"minLength",params:{limit: 2},message:"must NOT have fewer than 2 characters"}];return false;}}}}var valid2 = _errs38 === errors;}else {var valid2 = true;}if(valid2){if(data7.parent_url !== undefined){let data12 = data7.parent_url;const _errs41 = errors;if((typeof data12 !== "string") && (data12 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata/parent_url",schemaPath:"#/properties/metadata/properties/parent_url/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs41){if(typeof data12 === "string"){if(func5(data12) > 1000){validate20.errors = [{instancePath:instancePath+"/metadata/parent_url",schemaPath:"#/properties/metadata/properties/parent_url/maxLength",keyword:"maxLength",params:{limit: 1000},message:"must NOT have more than 1000 characters"}];return false;}else {if(func5(data12) < 5){validate20.errors = [{instancePath:instancePath+"/metadata/parent_url",schemaPath:"#/properties/metadata/properties/parent_url/minLength",keyword:"minLength",params:{limit: 5},message:"must NOT have fewer than 5 characters"}];return false;}}}}var valid2 = _errs41 === errors;}else {var valid2 = true;}if(valid2){if(data7.parent_host !== undefined){let data13 = data7.parent_host;const _errs44 = errors;if((typeof data13 !== "string") && (data13 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata/parent_host",schemaPath:"#/properties/metadata/properties/parent_host/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs44){if(typeof data13 === "string"){if(func5(data13) > 100){validate20.errors = [{instancePath:instancePath+"/metadata/parent_host",schemaPath:"#/properties/metadata/properties/parent_host/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data13) < 5){validate20.errors = [{instancePath:instancePath+"/metadata/parent_host",schemaPath:"#/properties/metadata/properties/parent_host/minLength",keyword:"minLength",params:{limit: 5},message:"must NOT have fewer than 5 characters"}];return false;}}}}var valid2 = _errs44 === errors;}else {var valid2 = true;}if(valid2){if(data7.session !== undefined){let data14 = data7.session;const _errs47 = errors;if((typeof data14 !== "string") && (data14 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata/session",schemaPath:"#/properties/metadata/properties/session/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs47){if(typeof data14 === "string"){if(func5(data14) > 100){validate20.errors = [{instancePath:instancePath+"/metadata/session",schemaPath:"#/properties/metadata/properties/session/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data14) < 1){validate20.errors = [{instancePath:instancePath+"/metadata/session",schemaPath:"#/properties/metadata/properties/session/minLength",keyword:"minLength",params:{limit: 1},message:"must NOT have fewer than 1 characters"}];return false;}}}}var valid2 = _errs47 === errors;}else {var valid2 = true;}if(valid2){if(data7.device !== undefined){let data15 = data7.device;const _errs50 = errors;if((typeof data15 !== "string") && (data15 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata/device",schemaPath:"#/properties/metadata/properties/device/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs50){if(typeof data15 === "string"){if(func5(data15) > 100){validate20.errors = [{instancePath:instancePath+"/metadata/device",schemaPath:"#/properties/metadata/properties/device/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data15) < 1){validate20.errors = [{instancePath:instancePath+"/metadata/device",schemaPath:"#/properties/metadata/properties/device/minLength",keyword:"minLength",params:{limit: 1},message:"must NOT have fewer than 1 characters"}];return false;}}}}var valid2 = _errs50 === errors;}else {var valid2 = true;}if(valid2){if(data7.shared_prompt !== undefined){let data16 = data7.shared_prompt;const _errs53 = errors;if((!(((typeof data16 == "number") && (!(data16 % 1) && !isNaN(data16))) && (isFinite(data16)))) && (data16 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata/shared_prompt",schemaPath:"#/properties/metadata/properties/shared_prompt/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid2 = _errs53 === errors;}else {var valid2 = true;}if(valid2){if(data7.translation !== undefined){let data17 = data7.translation;const _errs56 = errors;if((typeof data17 !== "string") && (data17 !== null)){validate20.errors = [{instancePath:instancePath+"/metadata/translation",schemaPath:"#/properties/metadata/properties/translation/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs56){if(typeof data17 === "string"){if(func5(data17) > 4){validate20.errors = [{instancePath:instancePath+"/metadata/translation",schemaPath:"#/properties/metadata/properties/translation/maxLength",keyword:"maxLength",params:{limit: 4},message:"must NOT have more than 4 characters"}];return false;}else {if(func5(data17) < 3){validate20.errors = [{instancePath:instancePath+"/metadata/translation",schemaPath:"#/properties/metadata/properties/translation/minLength",keyword:"minLength",params:{limit: 3},message:"must NOT have fewer than 3 characters"}];return false;}}}}var valid2 = _errs56 === errors;}else {var valid2 = true;}}}}}}}}}}}}var valid1 = _errs26 === errors;}else {var valid1 = true;}if(valid1){if(data.modalities !== undefined){let data18 = data.modalities;const _errs59 = errors;if((!(Array.isArray(data18))) && (data18 !== null)){validate20.errors = [{instancePath:instancePath+"/modalities",schemaPath:"#/properties/modalities/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}var valid1 = _errs59 === errors;}else {var valid1 = true;}if(valid1){if(data.model !== undefined){let data19 = data.model;const _errs62 = errors;if((typeof data19 !== "string") && (data19 !== null)){validate20.errors = [{instancePath:instancePath+"/model",schemaPath:"#/properties/model/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs62){if(typeof data19 === "string"){if(func5(data19) > 100){validate20.errors = [{instancePath:instancePath+"/model",schemaPath:"#/properties/model/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data19) < 1){validate20.errors = [{instancePath:instancePath+"/model",schemaPath:"#/properties/model/minLength",keyword:"minLength",params:{limit: 1},message:"must NOT have fewer than 1 characters"}];return false;}}}}var valid1 = _errs62 === errors;}else {var valid1 = true;}if(valid1){if(data.n !== undefined){let data20 = data.n;const _errs65 = errors;if((!(((typeof data20 == "number") && (!(data20 % 1) && !isNaN(data20))) && (isFinite(data20)))) && (data20 !== null)){validate20.errors = [{instancePath:instancePath+"/n",schemaPath:"#/properties/n/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs65 === errors;}else {var valid1 = true;}if(valid1){if(data.parallel_tool_calls !== undefined){let data21 = data.parallel_tool_calls;const _errs68 = errors;if((typeof data21 !== "boolean") && (data21 !== null)){validate20.errors = [{instancePath:instancePath+"/parallel_tool_calls",schemaPath:"#/properties/parallel_tool_calls/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];return false;}var valid1 = _errs68 === errors;}else {var valid1 = true;}if(valid1){if(data.prediction !== undefined){let data22 = data.prediction;const _errs71 = errors;if((!(data22 && typeof data22 == "object" && !Array.isArray(data22))) && (data22 !== null)){validate20.errors = [{instancePath:instancePath+"/prediction",schemaPath:"#/properties/prediction/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}var valid1 = _errs71 === errors;}else {var valid1 = true;}if(valid1){if(data.presence_penalty !== undefined){let data23 = data.presence_penalty;const _errs74 = errors;if((!((typeof data23 == "number") && (isFinite(data23)))) && (data23 !== null)){validate20.errors = [{instancePath:instancePath+"/presence_penalty",schemaPath:"#/properties/presence_penalty/type",keyword:"type",params:{type: "number"},message:"must be number"}];return false;}var valid1 = _errs74 === errors;}else {var valid1 = true;}if(valid1){if(data.prompt !== undefined){let data24 = data.prompt;const _errs77 = errors;if((typeof data24 !== "string") && (data24 !== null)){validate20.errors = [{instancePath:instancePath+"/prompt",schemaPath:"#/properties/prompt/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs77){if(typeof data24 === "string"){if(func5(data24) > 100000){validate20.errors = [{instancePath:instancePath+"/prompt",schemaPath:"#/properties/prompt/maxLength",keyword:"maxLength",params:{limit: 100000},message:"must NOT have more than 100000 characters"}];return false;}else {if(func5(data24) < 2){validate20.errors = [{instancePath:instancePath+"/prompt",schemaPath:"#/properties/prompt/minLength",keyword:"minLength",params:{limit: 2},message:"must NOT have fewer than 2 characters"}];return false;}}}}var valid1 = _errs77 === errors;}else {var valid1 = true;}if(valid1){if(data.reasoning_effort !== undefined){let data25 = data.reasoning_effort;const _errs80 = errors;if((typeof data25 !== "string") && (data25 !== null)){validate20.errors = [{instancePath:instancePath+"/reasoning_effort",schemaPath:"#/properties/reasoning_effort/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(!(((data25 === "low") || (data25 === "medium")) || (data25 === "high"))){validate20.errors = [{instancePath:instancePath+"/reasoning_effort",schemaPath:"#/properties/reasoning_effort/enum",keyword:"enum",params:{allowedValues: schema22.properties.reasoning_effort.enum},message:"must be equal to one of the allowed values"}];return false;}var valid1 = _errs80 === errors;}else {var valid1 = true;}if(valid1){if(data.verbosity !== undefined){let data26 = data.verbosity;const _errs83 = errors;if((typeof data26 !== "string") && (data26 !== null)){validate20.errors = [{instancePath:instancePath+"/verbosity",schemaPath:"#/properties/verbosity/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(!((((data26 === "minimal") || (data26 === "low")) || (data26 === "medium")) || (data26 === "high"))){validate20.errors = [{instancePath:instancePath+"/verbosity",schemaPath:"#/properties/verbosity/enum",keyword:"enum",params:{allowedValues: schema22.properties.verbosity.enum},message:"must be equal to one of the allowed values"}];return false;}var valid1 = _errs83 === errors;}else {var valid1 = true;}if(valid1){if(data.response_format !== undefined){let data27 = data.response_format;const _errs86 = errors;if((!(data27 && typeof data27 == "object" && !Array.isArray(data27))) && (data27 !== null)){validate20.errors = [{instancePath:instancePath+"/response_format",schemaPath:"#/properties/response_format/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}if(errors === _errs86){if(data27 && typeof data27 == "object" && !Array.isArray(data27)){if(data27.type !== undefined){let data28 = data27.type;if((typeof data28 !== "string") && (data28 !== null)){validate20.errors = [{instancePath:instancePath+"/response_format/type",schemaPath:"#/properties/response_format/properties/type/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(!((((data28 === "text") || (data28 === "html")) || (data28 === "json")) || (data28 === "raw"))){validate20.errors = [{instancePath:instancePath+"/response_format/type",schemaPath:"#/properties/response_format/properties/type/enum",keyword:"enum",params:{allowedValues: schema22.properties.response_format.properties.type.enum},message:"must be equal to one of the allowed values"}];return false;}}}}var valid1 = _errs86 === errors;}else {var valid1 = true;}if(valid1){if(data.seed !== undefined){let data29 = data.seed;const _errs92 = errors;if((!(((typeof data29 == "number") && (!(data29 % 1) && !isNaN(data29))) && (isFinite(data29)))) && (data29 !== null)){validate20.errors = [{instancePath:instancePath+"/seed",schemaPath:"#/properties/seed/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs92 === errors;}else {var valid1 = true;}if(valid1){if(data.service_tier !== undefined){let data30 = data.service_tier;const _errs95 = errors;if((typeof data30 !== "string") && (data30 !== null)){validate20.errors = [{instancePath:instancePath+"/service_tier",schemaPath:"#/properties/service_tier/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs95 === errors;}else {var valid1 = true;}if(valid1){if(data.stop !== undefined){let data31 = data.stop;const _errs98 = errors;if((typeof data31 !== "string") && (data31 !== null)){validate20.errors = [{instancePath:instancePath+"/stop",schemaPath:"#/properties/stop/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs98 === errors;}else {var valid1 = true;}if(valid1){if(data.store !== undefined){let data32 = data.store;const _errs101 = errors;if((typeof data32 !== "boolean") && (data32 !== null)){validate20.errors = [{instancePath:instancePath+"/store",schemaPath:"#/properties/store/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];return false;}var valid1 = _errs101 === errors;}else {var valid1 = true;}if(valid1){if(data.stream !== undefined){const _errs104 = errors;if(typeof data.stream !== "boolean"){validate20.errors = [{instancePath:instancePath+"/stream",schemaPath:"#/properties/stream/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];return false;}var valid1 = _errs104 === errors;}else {var valid1 = true;}if(valid1){if(data.stream_options !== undefined){let data34 = data.stream_options;const _errs107 = errors;if((!(data34 && typeof data34 == "object" && !Array.isArray(data34))) && (data34 !== null)){validate20.errors = [{instancePath:instancePath+"/stream_options",schemaPath:"#/properties/stream_options/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}var valid1 = _errs107 === errors;}else {var valid1 = true;}if(valid1){if(data.temperature !== undefined){let data35 = data.temperature;const _errs110 = errors;if((!((typeof data35 == "number") && (isFinite(data35)))) && (data35 !== null)){validate20.errors = [{instancePath:instancePath+"/temperature",schemaPath:"#/properties/temperature/type",keyword:"type",params:{type: "number"},message:"must be number"}];return false;}var valid1 = _errs110 === errors;}else {var valid1 = true;}if(valid1){if(data.top_p !== undefined){let data36 = data.top_p;const _errs113 = errors;if((!((typeof data36 == "number") && (isFinite(data36)))) && (data36 !== null)){validate20.errors = [{instancePath:instancePath+"/top_p",schemaPath:"#/properties/top_p/type",keyword:"type",params:{type: "number"},message:"must be number"}];return false;}var valid1 = _errs113 === errors;}else {var valid1 = true;}if(valid1){if(data.tools !== undefined){let data37 = data.tools;const _errs116 = errors;if((!(Array.isArray(data37))) && (data37 !== null)){validate20.errors = [{instancePath:instancePath+"/tools",schemaPath:"#/properties/tools/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}var valid1 = _errs116 === errors;}else {var valid1 = true;}if(valid1){if(data.tool_choice !== undefined){let data38 = data.tool_choice;const _errs119 = errors;if((!(data38 && typeof data38 == "object" && !Array.isArray(data38))) && (data38 !== null)){validate20.errors = [{instancePath:instancePath+"/tool_choice",schemaPath:"#/properties/tool_choice/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}var valid1 = _errs119 === errors;}else {var valid1 = true;}if(valid1){if(data.user !== undefined){let data39 = data.user;const _errs122 = errors;if((typeof data39 !== "string") && (data39 !== null)){validate20.errors = [{instancePath:instancePath+"/user",schemaPath:"#/properties/user/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs122){if(typeof data39 === "string"){if(func5(data39) > 100){validate20.errors = [{instancePath:instancePath+"/user",schemaPath:"#/properties/user/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data39) < 1){validate20.errors = [{instancePath:instancePath+"/user",schemaPath:"#/properties/user/minLength",keyword:"minLength",params:{limit: 1},message:"must NOT have fewer than 1 characters"}];return false;}}}}var valid1 = _errs122 === errors;}else {var valid1 = true;}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}}else {validate20.errors = [{instancePath,schemaPath:"#/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}}validate20.errors = vErrors;return errors === 0;}