{"type": "object", "properties": {"audio": {"type": "object", "nullable": true}, "frequency_penalty": {"type": "number", "nullable": true}, "logit_bias": {"type": "object", "nullable": true}, "logprobs": {"type": "object", "nullable": true}, "max_completion_tokens": {"type": "integer", "nullable": true}, "max_tokens": {"type": "integer", "nullable": true}, "messages": {"type": "array", "nullable": true}, "metadata": {"type": "object", "nullable": true, "properties": {"anonymous": {"type": "boolean", "nullable": false}, "conversation": {"type": "string", "nullable": true, "minLength": 1, "maxLength": 100}, "max_memories": {"type": "integer", "nullable": true}, "language": {"type": "string", "nullable": true, "minLength": 2, "maxLength": 10}, "parent_url": {"type": "string", "nullable": true, "minLength": 5, "maxLength": 1000}, "parent_host": {"type": "string", "nullable": true, "minLength": 5, "maxLength": 100}, "session": {"type": "string", "nullable": true, "minLength": 1, "maxLength": 100}, "device": {"type": "string", "nullable": true, "minLength": 1, "maxLength": 100}, "shared_prompt": {"type": "integer", "nullable": true}, "translation": {"type": "string", "nullable": true, "minLength": 3, "maxLength": 4}}}, "modalities": {"type": "array", "nullable": true}, "model": {"type": "string", "nullable": true, "minLength": 1, "maxLength": 100}, "n": {"type": "integer", "nullable": true}, "parallel_tool_calls": {"type": "boolean", "nullable": true}, "prediction": {"type": "object", "nullable": true}, "presence_penalty": {"type": "number", "nullable": true}, "prompt": {"type": "string", "nullable": true, "minLength": 2, "maxLength": 100000}, "reasoning_effort": {"type": "string", "nullable": true, "enum": ["low", "medium", "high"]}, "verbosity": {"type": "string", "nullable": true, "enum": ["minimal", "low", "medium", "high"]}, "response_format": {"type": "object", "nullable": true, "properties": {"type": {"type": "string", "nullable": true, "enum": ["text", "html", "json", "raw"]}}}, "seed": {"type": "integer", "nullable": true}, "service_tier": {"type": "string", "nullable": true}, "stop": {"type": "string", "nullable": true}, "store": {"type": "boolean", "nullable": true}, "stream": {"type": "boolean", "nullable": false}, "stream_options": {"type": "object", "nullable": true}, "temperature": {"type": "number", "nullable": true}, "top_p": {"type": "number", "nullable": true}, "tools": {"type": "array", "nullable": true}, "tool_choice": {"type": "object", "nullable": true}, "user": {"type": "string", "nullable": true, "minLength": 1, "maxLength": 100}}, "required": [], "additionalProperties": false, "oneOf": [{"required": ["prompt"]}, {"required": ["messages"]}]}