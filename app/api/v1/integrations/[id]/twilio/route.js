import {
    getDbTranslation,
    debug,
} from '../../../../../lib/helpers';
import twilio from 'twilio';
import {
    getIntegrationById,
    getNumPromptsByIntegrationConversationId,
    insertIntegrationMessage,
    getRecentIntegrationMessages
} from '../../../../../lib/db';
import { detectLanguage } from '../../../../../lib/language';
import { integrationShouldRespond } from '../../../../../lib/integration';
import { decryptBackend } from '../../../../../lib/crypt';
import { handleCompletion, createMessageObject } from '../../../../../lib/completion';
import {
    get200Response,
    get403Response,
    get500Response,
    get503Response,
} from '../../../../../lib/response';
import { getAgentByHost } from '../../../../../lib/agent';

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';
export const maxDuration = (parseInt(process.env.RESPONSE_TIMEOUT) / 1000);

// Handle the chat completion request
export async function POST(req, { params }) {

    const { id } = params;
    const integration = await getIntegrationById(id);
    if (!integration) {
        return get503Response();
    }

    // Get the payload
    const reqPayload = Object.fromEntries(new URLSearchParams(await req.text()));
    const conversationId = reqPayload.From;
    const prompt = reqPayload.Body;

    const lng = await detectLanguage(prompt);
    const cuePhrase = getDbTranslation(integration.cue, lng);

    // Log the message
    const messageId = await insertIntegrationMessage({
        message: prompt,
        language: lng,
        integration_id: integration.id,
        conversation_id: conversationId,
        user_id: reqPayload.From,
    });

    if (integrationShouldRespond(prompt, cuePhrase)) {

        const client = twilio(integration.account, await decryptBackend(integration.secret));
        if (!client) {
            return get403Response('Incorrect Twilio Credentials');
        }

        const agent = await getAgentByHost(req.headers.get('host'));
        const numPreviousResponses = await getNumPromptsByIntegrationConversationId(agent.id, integration.id, conversationId);

        // If there is no record of previous responses in this conversation, send the welcome message if it exists
        const welcome = getDbTranslation(integration.welcome, lng);
        if ((numPreviousResponses === 0) && welcome) {
            const message = await client.messages.create({
                from: reqPayload.To,
                to: reqPayload.From,
                body: welcome,
            });
        }

        const messageRows = await getRecentIntegrationMessages(integration.id, conversationId, parseInt(agent.max_memories) * 2);
        let messages = [];
        messageRows.reverse().forEach((message) => {
            messages.push(createMessageObject(message.user_id ? 'user' : 'assistant', message.message));
        });

        const payload = {
            messages: messages,
            stream: false,
            metadata: {
                session: conversationId,
                conversation: conversationId,
                language: lng,
            },
            user: conversationId,
            response_format: { type: 'json' },
        };

        const res = await handleCompletion(
            payload,
            req,
            integration
        );

        if (res.status === 200) {

            const json = await res.json();
            let response = json.choices[0].message.content;
            const cta = json.cta;
            if (cta) {
                response += `\n\n${cta.content}`;
            }

            try {

                const message = await client.messages.create({
                    from: reqPayload.To,
                    to: reqPayload.From,
                    body: response,
                });

                const messageId = await insertIntegrationMessage({
                    message: response,
                    language: lng,
                    integration_id: integration.id,
                    conversation_id: conversationId,
                    user_id: null,
                });

            } catch (error) {
                const errorMsg = `Error sending Twilio message: ${error.message}`;
                debug(true, 'ERROR', errorMsg);
                return get500Response(errorMsg);
            }

        } else {
            return res;
        }

    } else {
        // debug(`NO CUE PHRASE (${cuePhrase}): ${prompt}`);
    }

    return get200Response(null);

    // SmsMessageSid=SM85008720a29db38111de7cd7614a576f&NumMedia=0&ProfileName=Jake&MessageType=text&SmsSid=SM85008720a29db38111de7cd7614a576f&WaId=***********&SmsStatus=received&Body=Hello&To=whatsapp%3A%2B14155238886&NumSegments=1&ReferralNumMedia=0&MessageSid=SM85008720a29db38111de7cd7614a576f&AccountSid=AC61f47d03e4570334a766ce30a1ce2b3f&From=whatsapp%3A%2B***********&ApiVersion=2010-04-01

}
