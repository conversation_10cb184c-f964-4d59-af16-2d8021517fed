import {
    getAgentByHost,
    getModelProvider,
    agentIsInactive,
    getAgentTokenFromHeader,
    getAgentTokenFromQuery,
} from '../../../lib/agent';
import { getAgentModelById, getAgentModelByKey, getPromptTexts } from '../../../lib/db';
import { get503Response, get403Response, get422Response } from '../../../lib/response';
import { getPayloadValidation, requestIsSameOrigin } from '../../../lib/request';
import { debug } from '../../../lib/helpers';
import { sql } from '@vercel/postgres';

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = process.env.DEFAULT_RUNTIME ?? 'edge';
export const maxDuration = parseInt(process.env.RESPONSE_TIMEOUT) / 1000;

const API_KEY_HEADER = 'x-api-key';
const API_KEY_PARAM = 'api_key';

// Payload schema validator -- had to precompile b/c the compiler won't run on edge runtime
const validatePayload = require('./validatePayload');

// Handle the chat completion request
export async function POST(req) {

    const startTime = performance.now();

    // Validate the payload
    const payload = await req.json();
    const validation = await getPayloadValidation(payload, validatePayload);
    if (validation) {
        return get422Response(validation);
    }

    // Get Agent by hostname; bail if there's no match
    const AGENT = await getAgentByHost(req.headers.get('host'));
    if (agentIsInactive(AGENT)) {
        return get503Response();
    }

    // Bail if this is an API request and the API token is invalid
    const sameOrigin = requestIsSameOrigin(req.headers);
    const tokenId = await getAgentTokenFromHeader(AGENT.id, req.headers, API_KEY_HEADER) ||
        await getAgentTokenFromQuery(AGENT.id, req.nextUrl.searchParams, API_KEY_PARAM)
    ;
    if (!sameOrigin && !tokenId) {
        return get403Response();
    }

    // Determine the model to query
    const MODEL = payload.model ? await getAgentModelByKey(payload.model) : await getAgentModelById(AGENT.model_id);
    const PROVIDER = getModelProvider(MODEL);

    // Default the message limit
    if (!payload.message_limit) {
        payload.message_limit = parseInt(process.env.SOS_DEFAULT_MESSAGE_LIMIT);
    }

    let userMessages = [];
    if (payload.messages) {
        userMessages = payload.messages;
    } else if (payload.prompt) {
        userMessages.push(payload.prompt);
    } else {
        const params = {};
        if (payload.prompt_id) {
            params.prompt_id = payload.prompt_id;
        } else if (payload.conversation) {
            params.conversation_id = payload.conversation;
        } else if (payload.session) {
            params.session_id = payload.session;
        } else if (payload.user) {
            params.user_id = payload.user;
        }
        userMessages = await getPromptTexts(AGENT.id, params, payload.message_limit);
    }

    debug(true, 'MESSAGES', userMessages);


    /*
    // Generate the messages array
    let messages = [];
    if (payload.messages && (payload.messages.length > 1)) {
        messages = payload.messages;
    } else {
        const numMemories = payload.metadata.anonymous ? 0 : (payload.metadata.max_memories ?? AGENT.model_max_memories ?? process.env.COMPLETION_API_MEMORIES);
        messages = await getMessages(
            systemPrompt,
            payload.metadata.session,
            payload.metadata.conversation,
            promptId,
            translatedPrompt,
            payload.metadata.shared_prompt ?? null,
            numMemories,
            realTimeTranslation
        );
    }

    // Set the chat completion request body
    const maxTokensField = (PROVIDER.deprecations && PROVIDER.deprecations.includes('max_tokens')) ?
        'max_tokens' :
        'max_completion_tokens'
    ;
    const completionQry = getCompletionQuery(
        payload,
        AGENT,
        PROVIDER,
        MODEL,
        systemPrompt,
        messages,
        maxTokensField,
        PROVIDER.custom ?? {}
    );

    // Send the chat completion API request
    const endpointPath = PROVIDER.path ?? process.env.COMPLETION_API_PATH;
    // debug(endpointPath);

    const responseParams = {
        id: promptId,
        sources: ctx.sources,
        object: 'chat.completion',
        created: Math.floor(new Date().getTime() / 1000),
        model: MODEL.key,
        stream: payload.stream,
        metadata: payload.metadata,
        user: payload.user,
        usage: {},
        timings: {},
        system_fingerprint: configId,
    };

    // Set the response options
    const responseOptions = {
        status: 200,
        headers: {
            'Content-Type': CONTENT_TYPES[payload.response_format.type],
        },
    };

    // Log the prompt response start time
    waitUntil(setCompletionStart(promptId));


        const completion = await generateText(completionQry);
        let reasoning = null;
        let response = completion.text;
        const reasoningEndPos = completion.text.indexOf(REASONING_END_SEQUENCE);
        if (reasoningEndPos !== -1) {
            response = completion.text.substring(reasoningEndPos + REASONING_END_SEQUENCE.length).trim();
            reasoning = completion.text.substring(REASONING_START_SEQUENCE.length, reasoningEndPos).trim();
        }
        debug(completion);

        const translatedCompletion = await translateToLanguage(
            response,
            process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE,
            payload.metadata.language
        );
        waitUntil(setCompletionFinish(promptId, prompt, messages, response, reasoning, realTimeTranslation, payload.metadata.language));

        const transformedCompletion = transformCompletion(translatedCompletion, (payload.response_format.type === 'html'));

        let formattedCompletion = transformedCompletion;
        if (payload.response_format.type === 'json') {
            formattedCompletion = responseParams;
            formattedCompletion.choices = [
                {
                    index: 0,
                    message: {
                        role: "assistant",
                        content: transformedCompletion,
                    },
                    logprobs: null,
                    finish_reason: 'stop'
                }
            ];
            formattedCompletion = JSON.stringify(decorateCompletionParams(formattedCompletion, messages, response, reasoning, startTime));
        }

        return new Response(
            formattedCompletion,
            responseOptions
        );


     */

}

