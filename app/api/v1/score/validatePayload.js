"use strict";module.exports = validate20;module.exports.default = validate20;const schema22 = {"type":"object","properties":{"conversation":{"type":"string","nullable":true,"minLength":1,"maxLength":100},"language":{"type":"string","nullable":true,"minLength":2,"maxLength":10},"messages":{"type":"array","nullable":true},"message_limit":{"type":"integer","nullable":true},"model":{"type":"string","nullable":true,"minLength":1,"maxLength":100},"prompt":{"type":"string","nullable":true,"minLength":2,"maxLength":100000},"prompt_id":{"type":"string","nullable":true},"session":{"type":"string","nullable":true,"minLength":1,"maxLength":100},"user":{"type":"string","nullable":true,"minLength":1,"maxLength":100}},"required":[],"additionalProperties":false,"oneOf":[{"required":["conversation"]},{"required":["messages"]},{"required":["prompt"]},{"required":["prompt_id"]},{"required":["session"]},{"required":["user"]}]};const func4 = Object.prototype.hasOwnProperty;const func5 = require("ajv/dist/runtime/ucs2length").default;function validate20(data, {instancePath="", parentData, parentDataProperty, rootData=data}={}){let vErrors = null;let errors = 0;const _errs1 = errors;let valid0 = false;let passing0 = null;const _errs2 = errors;if(data && typeof data == "object" && !Array.isArray(data)){let missing0;if((data.conversation === undefined) && (missing0 = "conversation")){const err0 = {instancePath,schemaPath:"#/oneOf/0/required",keyword:"required",params:{missingProperty: missing0},message:"must have required property '"+missing0+"'"};if(vErrors === null){vErrors = [err0];}else {vErrors.push(err0);}errors++;}}var _valid0 = _errs2 === errors;if(_valid0){valid0 = true;passing0 = 0;}const _errs3 = errors;if(data && typeof data == "object" && !Array.isArray(data)){let missing1;if((data.messages === undefined) && (missing1 = "messages")){const err1 = {instancePath,schemaPath:"#/oneOf/1/required",keyword:"required",params:{missingProperty: missing1},message:"must have required property '"+missing1+"'"};if(vErrors === null){vErrors = [err1];}else {vErrors.push(err1);}errors++;}}var _valid0 = _errs3 === errors;if(_valid0 && valid0){valid0 = false;passing0 = [passing0, 1];}else {if(_valid0){valid0 = true;passing0 = 1;}const _errs4 = errors;if(data && typeof data == "object" && !Array.isArray(data)){let missing2;if((data.prompt === undefined) && (missing2 = "prompt")){const err2 = {instancePath,schemaPath:"#/oneOf/2/required",keyword:"required",params:{missingProperty: missing2},message:"must have required property '"+missing2+"'"};if(vErrors === null){vErrors = [err2];}else {vErrors.push(err2);}errors++;}}var _valid0 = _errs4 === errors;if(_valid0 && valid0){valid0 = false;passing0 = [passing0, 2];}else {if(_valid0){valid0 = true;passing0 = 2;}const _errs5 = errors;if(data && typeof data == "object" && !Array.isArray(data)){let missing3;if((data.prompt_id === undefined) && (missing3 = "prompt_id")){const err3 = {instancePath,schemaPath:"#/oneOf/3/required",keyword:"required",params:{missingProperty: missing3},message:"must have required property '"+missing3+"'"};if(vErrors === null){vErrors = [err3];}else {vErrors.push(err3);}errors++;}}var _valid0 = _errs5 === errors;if(_valid0 && valid0){valid0 = false;passing0 = [passing0, 3];}else {if(_valid0){valid0 = true;passing0 = 3;}const _errs6 = errors;if(data && typeof data == "object" && !Array.isArray(data)){let missing4;if((data.session === undefined) && (missing4 = "session")){const err4 = {instancePath,schemaPath:"#/oneOf/4/required",keyword:"required",params:{missingProperty: missing4},message:"must have required property '"+missing4+"'"};if(vErrors === null){vErrors = [err4];}else {vErrors.push(err4);}errors++;}}var _valid0 = _errs6 === errors;if(_valid0 && valid0){valid0 = false;passing0 = [passing0, 4];}else {if(_valid0){valid0 = true;passing0 = 4;}const _errs7 = errors;if(data && typeof data == "object" && !Array.isArray(data)){let missing5;if((data.user === undefined) && (missing5 = "user")){const err5 = {instancePath,schemaPath:"#/oneOf/5/required",keyword:"required",params:{missingProperty: missing5},message:"must have required property '"+missing5+"'"};if(vErrors === null){vErrors = [err5];}else {vErrors.push(err5);}errors++;}}var _valid0 = _errs7 === errors;if(_valid0 && valid0){valid0 = false;passing0 = [passing0, 5];}else {if(_valid0){valid0 = true;passing0 = 5;}}}}}}if(!valid0){const err6 = {instancePath,schemaPath:"#/oneOf",keyword:"oneOf",params:{passingSchemas: passing0},message:"must match exactly one schema in oneOf"};if(vErrors === null){vErrors = [err6];}else {vErrors.push(err6);}errors++;validate20.errors = vErrors;return false;}else {errors = _errs1;if(vErrors !== null){if(_errs1){vErrors.length = _errs1;}else {vErrors = null;}}}if(errors === 0){if(data && typeof data == "object" && !Array.isArray(data)){const _errs8 = errors;for(const key0 in data){if(!(func4.call(schema22.properties, key0))){validate20.errors = [{instancePath,schemaPath:"#/additionalProperties",keyword:"additionalProperties",params:{additionalProperty: key0},message:"must NOT have additional properties"}];return false;break;}}if(_errs8 === errors){if(data.conversation !== undefined){let data0 = data.conversation;const _errs9 = errors;if((typeof data0 !== "string") && (data0 !== null)){validate20.errors = [{instancePath:instancePath+"/conversation",schemaPath:"#/properties/conversation/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs9){if(typeof data0 === "string"){if(func5(data0) > 100){validate20.errors = [{instancePath:instancePath+"/conversation",schemaPath:"#/properties/conversation/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data0) < 1){validate20.errors = [{instancePath:instancePath+"/conversation",schemaPath:"#/properties/conversation/minLength",keyword:"minLength",params:{limit: 1},message:"must NOT have fewer than 1 characters"}];return false;}}}}var valid1 = _errs9 === errors;}else {var valid1 = true;}if(valid1){if(data.language !== undefined){let data1 = data.language;const _errs12 = errors;if((typeof data1 !== "string") && (data1 !== null)){validate20.errors = [{instancePath:instancePath+"/language",schemaPath:"#/properties/language/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs12){if(typeof data1 === "string"){if(func5(data1) > 10){validate20.errors = [{instancePath:instancePath+"/language",schemaPath:"#/properties/language/maxLength",keyword:"maxLength",params:{limit: 10},message:"must NOT have more than 10 characters"}];return false;}else {if(func5(data1) < 2){validate20.errors = [{instancePath:instancePath+"/language",schemaPath:"#/properties/language/minLength",keyword:"minLength",params:{limit: 2},message:"must NOT have fewer than 2 characters"}];return false;}}}}var valid1 = _errs12 === errors;}else {var valid1 = true;}if(valid1){if(data.messages !== undefined){let data2 = data.messages;const _errs15 = errors;if((!(Array.isArray(data2))) && (data2 !== null)){validate20.errors = [{instancePath:instancePath+"/messages",schemaPath:"#/properties/messages/type",keyword:"type",params:{type: "array"},message:"must be array"}];return false;}var valid1 = _errs15 === errors;}else {var valid1 = true;}if(valid1){if(data.message_limit !== undefined){let data3 = data.message_limit;const _errs18 = errors;if((!(((typeof data3 == "number") && (!(data3 % 1) && !isNaN(data3))) && (isFinite(data3)))) && (data3 !== null)){validate20.errors = [{instancePath:instancePath+"/message_limit",schemaPath:"#/properties/message_limit/type",keyword:"type",params:{type: "integer"},message:"must be integer"}];return false;}var valid1 = _errs18 === errors;}else {var valid1 = true;}if(valid1){if(data.model !== undefined){let data4 = data.model;const _errs21 = errors;if((typeof data4 !== "string") && (data4 !== null)){validate20.errors = [{instancePath:instancePath+"/model",schemaPath:"#/properties/model/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs21){if(typeof data4 === "string"){if(func5(data4) > 100){validate20.errors = [{instancePath:instancePath+"/model",schemaPath:"#/properties/model/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data4) < 1){validate20.errors = [{instancePath:instancePath+"/model",schemaPath:"#/properties/model/minLength",keyword:"minLength",params:{limit: 1},message:"must NOT have fewer than 1 characters"}];return false;}}}}var valid1 = _errs21 === errors;}else {var valid1 = true;}if(valid1){if(data.prompt !== undefined){let data5 = data.prompt;const _errs24 = errors;if((typeof data5 !== "string") && (data5 !== null)){validate20.errors = [{instancePath:instancePath+"/prompt",schemaPath:"#/properties/prompt/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs24){if(typeof data5 === "string"){if(func5(data5) > 100000){validate20.errors = [{instancePath:instancePath+"/prompt",schemaPath:"#/properties/prompt/maxLength",keyword:"maxLength",params:{limit: 100000},message:"must NOT have more than 100000 characters"}];return false;}else {if(func5(data5) < 2){validate20.errors = [{instancePath:instancePath+"/prompt",schemaPath:"#/properties/prompt/minLength",keyword:"minLength",params:{limit: 2},message:"must NOT have fewer than 2 characters"}];return false;}}}}var valid1 = _errs24 === errors;}else {var valid1 = true;}if(valid1){if(data.prompt_id !== undefined){let data6 = data.prompt_id;const _errs27 = errors;if((typeof data6 !== "string") && (data6 !== null)){validate20.errors = [{instancePath:instancePath+"/prompt_id",schemaPath:"#/properties/prompt_id/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}var valid1 = _errs27 === errors;}else {var valid1 = true;}if(valid1){if(data.session !== undefined){let data7 = data.session;const _errs30 = errors;if((typeof data7 !== "string") && (data7 !== null)){validate20.errors = [{instancePath:instancePath+"/session",schemaPath:"#/properties/session/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs30){if(typeof data7 === "string"){if(func5(data7) > 100){validate20.errors = [{instancePath:instancePath+"/session",schemaPath:"#/properties/session/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data7) < 1){validate20.errors = [{instancePath:instancePath+"/session",schemaPath:"#/properties/session/minLength",keyword:"minLength",params:{limit: 1},message:"must NOT have fewer than 1 characters"}];return false;}}}}var valid1 = _errs30 === errors;}else {var valid1 = true;}if(valid1){if(data.user !== undefined){let data8 = data.user;const _errs33 = errors;if((typeof data8 !== "string") && (data8 !== null)){validate20.errors = [{instancePath:instancePath+"/user",schemaPath:"#/properties/user/type",keyword:"type",params:{type: "string"},message:"must be string"}];return false;}if(errors === _errs33){if(typeof data8 === "string"){if(func5(data8) > 100){validate20.errors = [{instancePath:instancePath+"/user",schemaPath:"#/properties/user/maxLength",keyword:"maxLength",params:{limit: 100},message:"must NOT have more than 100 characters"}];return false;}else {if(func5(data8) < 1){validate20.errors = [{instancePath:instancePath+"/user",schemaPath:"#/properties/user/minLength",keyword:"minLength",params:{limit: 1},message:"must NOT have fewer than 1 characters"}];return false;}}}}var valid1 = _errs33 === errors;}else {var valid1 = true;}}}}}}}}}}}else {validate20.errors = [{instancePath,schemaPath:"#/type",keyword:"type",params:{type: "object"},message:"must be object"}];return false;}}validate20.errors = vErrors;return errors === 0;}