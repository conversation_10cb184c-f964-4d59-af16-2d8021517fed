{"type": "object", "properties": {"conversation": {"type": "string", "nullable": true, "minLength": 1, "maxLength": 100}, "language": {"type": "string", "nullable": true, "minLength": 2, "maxLength": 10}, "messages": {"type": "array", "nullable": true}, "message_limit": {"type": "integer", "nullable": true}, "model": {"type": "string", "nullable": true, "minLength": 1, "maxLength": 100}, "prompt": {"type": "string", "nullable": true, "minLength": 2, "maxLength": 100000}, "prompt_id": {"type": "string", "nullable": true}, "session": {"type": "string", "nullable": true, "minLength": 1, "maxLength": 100}, "user": {"type": "string", "nullable": true, "minLength": 1, "maxLength": 100}}, "required": [], "additionalProperties": false, "oneOf": [{"required": ["conversation"]}, {"required": ["messages"]}, {"required": ["prompt"]}, {"required": ["prompt_id"]}, {"required": ["session"]}, {"required": ["user"]}]}