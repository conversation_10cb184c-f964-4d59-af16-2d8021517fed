{"type": "object", "properties": {"query": {"type": "string", "nullable": false}, "prompt_id": {"type": "string", "nullable": true}, "limit": {"type": "integer", "nullable": true}, "filters": {"type": "object", "properties": {"model": {"type": "string", "nullable": true}, "ids": {"type": "array", "nullable": true, "items": {"type": "integer"}}, "types": {"type": "array", "nullable": true, "items": {"type": "string"}}, "languages": {"type": "array", "nullable": true, "items": {"type": "string"}}, "collection_ids": {"type": "array", "nullable": true, "items": {"type": "integer"}}, "contributor_ids": {"type": "array", "nullable": true, "items": {"type": "integer"}}, "category_ids": {"type": "array", "nullable": true, "items": {"type": "integer"}}, "classification_ids": {"type": "array", "nullable": true, "items": {"type": "integer"}}, "external_ids": {"type": "array", "nullable": true, "items": {"type": "string"}}, "team_ids": {"type": "array", "nullable": true, "items": {"type": "integer"}}, "user_ids": {"type": "array", "nullable": true, "items": {"type": "integer"}}, "is_locked": {"type": "boolean", "nullable": true}, "min_weight": {"type": "integer", "nullable": true}, "max_weight": {"type": "integer", "nullable": true}, "min_rating": {"type": "integer", "nullable": true}, "max_rating": {"type": "integer", "nullable": true}, "min_num_views": {"type": "integer", "nullable": true}, "max_num_views": {"type": "integer", "nullable": true}, "min_num_impressions": {"type": "integer", "nullable": true}, "max_num_impressions": {"type": "integer", "nullable": true}, "min_num_referrals": {"type": "integer", "nullable": true}, "max_num_referrals": {"type": "integer", "nullable": true}, "min_published_on": {"type": "string", "nullable": true}, "max_published_on": {"type": "string", "nullable": true}, "min_created_at": {"type": "string", "nullable": true}, "max_created_at": {"type": "string", "nullable": true}, "min_updated_at": {"type": "string", "nullable": true}, "max_updated_at": {"type": "string", "nullable": true}}}}, "required": ["query"], "additionalProperties": false}