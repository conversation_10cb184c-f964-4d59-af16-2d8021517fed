import { handleSearch } from '../../../lib/search';

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = process.env.DEFAULT_RUNTIME ?? 'edge';
export const maxDuration = (parseInt(process.env.RESPONSE_TIMEOUT) / 1000);


// Handle the chat completion request
export async function POST(req) {

    const payload = await req.json();
    return handleSearch(
        payload,
        req
    );

}
