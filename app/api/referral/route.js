import { sql } from '@vercel/postgres';
import { NextResponse } from 'next/server';
import { insertItemReferral } from "../../lib/db";

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = process.env.DEFAULT_RUNTIME ?? 'edge';

export async function GET(req) {

    const item_model = req.nextUrl.searchParams.get('item_model');
    const item_id = req.nextUrl.searchParams.get('item_id');
    const prompt_id = req.nextUrl.searchParams.get('prompt_id');
    const user_id = req.nextUrl.searchParams.get('user_id');
    const url = req.nextUrl.searchParams.get('url');

    await insertItemReferral({
        frontend: 'agent',
        item_model: item_model,
        item_id: item_id,
        prompt_id: prompt_id,
        user_id: user_id,
    });

    return NextResponse.redirect(decodeURIComponent(url));

}
