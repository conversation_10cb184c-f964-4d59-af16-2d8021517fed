import { NextResponse } from 'next/server';

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

export async function POST(req) {

    const reqBody = await req.json();
    const reqHeaders = Object.fromEntries(await req.headers.entries());
    const reqQuery = Object.fromEntries(req.nextUrl.searchParams.entries());

    return NextResponse.json(JSON.stringify({ body: reqBody, headers: reqHeaders, query: reqQuery }), { status: 200 });

}
