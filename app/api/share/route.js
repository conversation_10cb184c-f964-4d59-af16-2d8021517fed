import { NextResponse } from 'next/server';
import { getSessionValue } from "../../lib/session";
import { insertShare } from "../../lib/db";

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = process.env.DEFAULT_RUNTIME ?? 'edge';

export async function POST(req) {

    const { shared_prompt_id, user_id, agent_id } = await req.json();

    const conversation_id = await getSessionValue('conversation_id');
    const session_id = await getSessionValue('session_id');

    await insertShare({
        shared_prompt_id: shared_prompt_id,
        conversation_id: conversation_id,
        session_id: session_id,
        user_id: user_id,
        agent_id: agent_id,
    });

    return NextResponse.json({ messsage: 'Share Successfully Logged' }, { status: 200 });

}
