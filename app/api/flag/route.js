import { NextResponse } from 'next/server';
import { updatePrompt } from "../../lib/db";

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = process.env.DEFAULT_RUNTIME ?? 'edge';

export async function POST(req) {

    const { prompt_id, conversation_id, flagged } = await req.json();

    await updatePrompt(
        prompt_id,
        {
            flagged: flagged,
        },
        null,
        {
            conversation_id: conversation_id,
        }
    );

    return NextResponse.json({ messsage: 'Response Successfully Flagged' }, { status: 200 });

}
