import { NextResponse } from 'next/server';
import { debug } from '../../lib/helpers';

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = 'edge';

const validatePayload = require('../v1/chat/completions/validation');

// THE PURPOSE OF THIS ENDPOINT IS TO TEST EDGE FUNCTIONS
export async function GET(req) {

    const validPayload = validatePayload({});
    debug(true, 'ERRORS', validatePayload.errors);
    return new NextResponse('OK');

}
