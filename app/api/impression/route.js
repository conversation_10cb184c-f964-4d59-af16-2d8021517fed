import { NextResponse } from 'next/server';
import { insertItemImpression } from "../../lib/db";

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = process.env.DEFAULT_RUNTIME ?? 'edge';

export async function POST(req) {

    const { prompt_id, source_id, user_id } = await req.json();

    await insertItemImpression({
        frontend: 'agent',
        item_model: 'source',
        item_id: source_id,
        prompt_id: prompt_id,
        user_id: user_id,
    });

    return NextResponse.json({ messsage: 'Impression Successfully Logged' }, { status: 200 });

}
