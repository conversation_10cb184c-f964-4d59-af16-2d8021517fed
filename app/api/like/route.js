import { NextResponse } from 'next/server';
import { updatePrompt } from "../../lib/db";

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = process.env.DEFAULT_RUNTIME ?? 'edge';

export async function POST(req) {

    const { prompt_id, conversation_id, liked } = await req.json();

    await updatePrompt(
        prompt_id,
        {
            liked: liked,
        },
        null,
        {
            conversation_id: conversation_id,
        }
    );

    return NextResponse.json({ messsage: 'Like Successfully Logged' }, { status: 200 });

}
