import { NextResponse } from 'next/server';
import { getPromptById } from '../../lib/db';
import { waitUntil } from '@vercel/functions';
import { setCompletionFinish } from '../../lib/completion';

// Runtime Options
export const dynamic = 'force-dynamic';
export const runtime = process.env.DEFAULT_RUNTIME ?? 'edge';

export async function POST(req) {

    const { prompt_id, conversation_id, messages, realtime_translation, lng, agent_id } = await req.json();

    // Validate prompt by comparing conversation
    const record = await getPromptById(prompt_id, agent_id);
    if (record && (record.conversation_id === conversation_id)) {
        const response = messages[messages.length-1].content;
        const prompt = messages[messages.length-2].content;
        waitUntil(setCompletionFinish(prompt_id, prompt, messages, response, null, realtime_translation, lng));
    }

    return NextResponse.json({ messsage: 'Response Successfully Logged' }, { status: 200 });

}
