import { debug } from './helpers';
import { callCorpusEndpoint, formatDocumentIds } from './vectara';

export async function getRagDocuments(agentId, ids) {

    const idStr = formatDocumentIds(ids.slice(0, process.env.RAG_NUM_RESULTS));
    const qry = {
        metadata_filter: `doc.id IN (${idStr})`,
    };
    // debug(qry);

    // Send API request to vector DB API
    const res = await callCorpusEndpoint(
        process.env.RAG_CORPUS_KEY,
        process.env.RAG_API_KEY,
        'GET',
        process.env.VECTARA_API_ENDPOINT_DOCS,
        qry
    );
    if (res) {
        let sources = [];
        res.documents.forEach((doc) => sources.push(expandRagDocumentAttrs(doc.id, doc.metadata)));
        return sources;
    } else {
        return null;
    }

}

// Expand RAG document meta data for source
export function expandRagDocumentAttrs(id, meta) {
    // Only include attributes that are needed; discard the rest
    let source = (({title, lang, image_url, authors, is_approved, weight, categories, tags, collections, contributors, classification, team, referral_url}) => ({title, lang, image_url, authors, is_approved, weight, categories, tags, collections, contributors, classification, team, referral_url}))(meta);
    source.id = id;
    return source;
}
