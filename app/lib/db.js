import * as tables from '../../db/schema';
import {
    and,
    asc,
    desc,
    eq,
    ne,
    or,
    lte,
    sql,
    isNotNull,
    isNull,
    arrayContains,
    getTableColumns
} from 'drizzle-orm';
import { getRagDocuments } from './rag';
import {
    getDbTranslation,
    debug
} from './helpers';

// Helper function to get database instance
const { db } = (process.env.NEXT_PUBLIC_ENV === 'local') ?
    require('../../db/drizzle.local') :
    require('../../db/drizzle')
;

export async function getAgentByDomain(host, includeFrontend = false) {
    const rows = await getAgent(
        or(
            eq(tables.agents.vanity_domain, host),
            eq(sql.raw("CONCAT(slug, '.', root_domain)"), host),
        ),
        includeFrontend
    );
    return handleMissingAgent(rows, includeFrontend);
}

export async function getDefaultAgent(includeFrontend = false) {
    const rows = await getAgent(
        and(
            eq(tables.agents.is_active, true),
            eq(tables.agents.is_approved, true),
        ),
        includeFrontend
    );
    return rows[0];
}

async function getAgent(conditions, includeFrontend = false) {

    let columns = {
        ...getTableColumns(tables.agents),
        model_languages: tables.agent_models.languages,
        has_custom_corpus: tables.teams.has_custom_corpus,
        corpus_api_key: tables.teams.corpus_api_key,
        team_is_active: tables.teams.is_active,
        is_trial: tables.teams.is_trial,
    };
    if (includeFrontend) {
        columns = {
            ...getTableColumns(tables.agent_frontends),
            ...columns,
        };
    }

    let qry = db
        .select(columns)
        .from(tables.agents)
        .innerJoin(tables.agent_models, eq(tables.agents.model_id, tables.agent_models.id))
        .innerJoin(tables.teams, eq(tables.agents.team_id, tables.teams.id))
        .where(conditions)
        .orderBy(asc(tables.agents.id))
        .limit(1)
    ;
    if (includeFrontend) {
        qry = qry.leftJoin(tables.agent_frontends, eq(tables.agents.id, tables.agent_frontends.agent_id));
    }

    return qry;

}

export async function handleMissingAgent(rows, includeFrontend = false) {
    if (rows.length > 0) {
        return rows[0];
    } else {
        return getDefaultAgent(includeFrontend);
    }
}

export async function getExtensibleAgentById(id, teamId) {
    const rows = await db
        .select({
            parent_id: tables.agents.parent_id,
            model_system_prompt: tables.agents.model_system_prompt,
            team_id: tables.agents.team_id,
        })
        .from(tables.agents)
        .innerJoin(tables.agent_models, eq(tables.agents.model_id, tables.agent_models.id))
        .where(
            and(
                eq(tables.agents.id, id),
                eq(tables.agents.is_approved, true),
                or(
                    eq(tables.agents.is_extensible, true),
                    eq(tables.agents.is_template, true),
                    eq(tables.agents.team_id, teamId)
                ),
            )
        )
    ;
    if (rows.length > 0) {
        return rows[0];
    } else {
        return null;
    }
}

export async function getAgentModelById(id) {
    return await getAgentModel(tables.agent_models.id, id);
}

export async function getAgentModelByKey(key) {
    return await getAgentModel(tables.agent_models.key, key.trim());
}

async function getAgentModel(conditionField, conditionValue) {
    const rows = await db
        .select({
            id: tables.agent_models.id,
            key: tables.agent_models.key,
            max_tokens: tables.agent_models.max_tokens,
            provider_model: tables.agent_models.provider_model,
            stop_sequence: tables.agent_models.stop_sequence,
            provider_key: tables.agent_model_providers.key,
            supports_reasoning_effort: tables.agent_models.supports_reasoning_effort,
            supports_verbosity: tables.agent_models.supports_verbosity,
            supports_temperature: tables.agent_models.supports_temperature,
            supports_top_p: tables.agent_models.supports_top_p,
            supports_frequency_penalty: tables.agent_models.supports_frequency_penalty,
            supports_presence_penalty: tables.agent_models.supports_presence_penalty,
            fallback_model_id: tables.agent_models.fallback_model_id,
        })
        .from(tables.agent_models)
        .innerJoin(tables.agent_model_providers, eq(tables.agent_models.provider_id, tables.agent_model_providers.id))
        .where(
            and(
                eq(conditionField, conditionValue),
                eq(tables.agent_models.is_active, true),
                eq(tables.agent_model_providers.is_active, true),
            )
        )
        .limit(1)
    ;
    return (rows.length > 0) ? rows[0] : null;
}

export async function getAgentQuestions(agentId, lng, limit = 4) {
    const rows = await db
        .select()
        .from(tables.agent_questions)
        .where(
            and(
                eq(tables.agent_questions.agent_id, agentId),
                eq(tables.agent_questions.is_active, true),
            )
        )
        .orderBy(sql`RANDOM()`)
        .limit(limit)
    ;
    return rows.map(function(row){ return { id: row.id, question: getDbTranslation(row.question, lng) } });
}

export async function getSharedPromptMessages(shareToken, agentId) {

    const token = atob(shareToken);
    debug(true, 'SHARE TOKEN', token);
    const tokenParts = token.split(':');
    const shareAgentId = parseInt(tokenParts[0]);
    const conversationIdPrefix = tokenParts[1];
    const promptId = parseInt(tokenParts[2]);

    if (shareAgentId === agentId) {
        const rows = await db
            .select({
                id: tables.prompts.id,
                prompt: tables.prompts.prompt,
                response: tables.prompts.response,
                conversation_id: tables.prompts.conversation_id,
                source_ids: sql.raw(`ARRAY_AGG(prompt_sources.source_id)`),
            })
            .from(tables.prompts)
            .innerJoin(tables.prompt_sources, eq(tables.prompts.id, tables.prompt_sources.prompt_id))
            .where(
                and(
                    eq(tables.prompts.agent_id, agentId),
                    or(
                        eq(sql.raw(`LEFT(prompts.conversation_id, ${conversationIdPrefix.length})`), conversationIdPrefix),
                        eq(tables.prompts.conversation_id, conversationIdPrefix),
                    ),
                    lte(tables.prompts.id, promptId),
                )
            )
            .groupBy(
                tables.prompts.id,
                tables.prompts.prompt,
                tables.prompts.response,
                tables.prompts.prompted_at,
                tables.prompts.conversation_id,
            )
            .orderBy(asc(tables.prompts.prompted_at))
        ;

        let sharedMessages = [];
        for (let prompt of rows) {

            if (prompt.source_ids && (prompt.source_ids.length > 0)) {
                prompt.sources = await getRagDocuments(agentId, prompt.source_ids);
            }

            sharedMessages.push(
                {
                    id: `shared-prompt-${prompt.id}`,
                    role: 'user',
                    metadata: {
                        id: prompt.id,
                        sources: [],
                        prompt: prompt.prompt,
                        metadata: {
                            conversation: prompt.conversation_id,
                        },
                    },
                    parts: [
                        {
                            type: 'text',
                            text: prompt.prompt,
                        }
                    ],
                },
                {
                    id: `shared-response-${prompt.id}`,
                    role: 'assistant',
                    metadata: {
                        id: prompt.id,
                        sources: prompt.sources,
                        prompt: prompt.prompt,
                        metadata: {
                            conversation: prompt.conversation_id,
                        },
                    },
                    parts: [
                        {
                            type: 'text',
                            text: prompt.response,
                        }
                    ],
                }
            );

        }

        return sharedMessages;

    }
    return [];
}

// Match an existing agent token
export async function getAgentToken(agentId, token) {
    const rows = await db
        .select()
        .from(tables.agent_tokens)
        .where(
            and(
                eq(tables.agent_tokens.agent_id, agentId),
                eq(tables.agent_tokens.token, sql.raw(`crypt('${token}', token)`)),
                eq(tables.agent_tokens.is_active, true),
            )
        )
        .orderBy(asc(tables.agent_tokens.id))
        .limit(1)
    ;
    return (rows.length > 0) ? rows[0] : null;
}

export async function getAgentConfig(params) {
    const conditions = [];
    for (const field in params) {
        conditions.push(
            (params[field] === null) ? isNull(tables.configs[field]) : eq(tables.configs[field], params[field])
        );
    }
    const rows = await db
        .select({
            id: tables.configs.id,
        })
        .from(tables.configs)
        .where(
            and(...conditions)
        )
        .limit(1)
    ;
    return (rows.length > 0) ? rows[0] : null;
}

export async function insertAgentConfig(params) {
    const records = await db
        .insert(tables.configs)
        .values(params)
        .returning({ id: tables.configs.id })
    ;
    return records[0];
}

export async function insertPrompt(params) {
    params.prompted_at = sql.raw("NOW() at time zone 'utc'");
    const records = await db
        .insert(tables.prompts)
        .values(params)
        .returning({ id: tables.prompts.id })
    ;
    // debug(records);
    return records[0];
}

export async function getRecentPrompts(
    currentPromptId,
    sharedPromptId,
    conversationId,
    sessionId,
    realTimeTranslation,
    limit
) {

    if (sharedPromptId || conversationId) {

        let orClauses = [];
        if (sharedPromptId) {
            orClauses.push(eq(tables.prompts.id, sharedPromptId));
        }
        if (conversationId) {
            orClauses.push(eq(tables.prompts.conversation_id, conversationId));
        }

        const prompts = await db
            .select({
                prompt: realTimeTranslation ? tables.prompts.translated_prompt : tables.prompts.prompt,
                response: realTimeTranslation ? tables.prompts.translated_response : tables.prompts.response,
            })
            .from(tables.prompts)
            .where(
                and(
                    ne(tables.prompts.id, currentPromptId),
                    or(...orClauses),
                    isNotNull(realTimeTranslation ? tables.prompts.translated_response : tables.prompts.response),
                )
            )
            .orderBy(desc(tables.prompts.prompted_at))
            .limit(limit)
        ;

        // console.log(prompts.toSQL());
        // console.log(prompts);
        return prompts;

    } else {
        return [];
    }

}

export async function updatePrompt(promptId, params = {}, updateTimestampField = null, additionalConditions = {}) {

    if (updateTimestampField) {
        params[updateTimestampField] = sql.raw("NOW() at time zone 'utc'");
    }

    const conditions = [eq(tables.prompts.id, promptId)];
    for (const field in additionalConditions) {
        conditions.push(eq(tables.prompts[field], additionalConditions[field]));
    }

    return db
        .update(tables.prompts)
        .set(params)
        .where(...conditions)
    ;

}

export async function insertPromptSource(promptId, sourceId, snippet, score) {
    return db
        .insert(tables.prompt_sources)
        .values({
            prompt_id: promptId,
            source_id: sourceId,
            snippet: snippet,
            score: score,
        })
    ;
}

export async function getSessionById(sessionId) {
    const rows = await db
        .select({
            id: tables.sessions.id,
        })
        .from(tables.sessions)
        .where(
            eq(tables.sessions.id, sessionId)
        )
        .limit(1)
    ;
    return (rows.length > 0) ? rows[0] : null;
}

export async function insertSession(params) {
    params.started_at = sql.raw("NOW() at time zone 'utc'");
    const records = await db
        .insert(tables.sessions)
        .values(params)
        .returning({ id: tables.sessions.id })
    ;
    // debug(records);
    return records[0];
}

export async function insertCtaClick(params) {
    params.clicked_at = sql.raw("NOW() at time zone 'utc'");
    return db
        .insert(tables.agent_cta_clicks)
        .values(params)
    ;
}

export async function insertItemReferral(params) {
    params.referred_at = sql.raw("NOW() at time zone 'utc'");
    return db
        .insert(tables.item_referrals)
        .values(params)
    ;
}

export async function insertItemView(params) {
    params.viewed_at = sql.raw("NOW() at time zone 'utc'");
    return db
        .insert(tables.item_views)
        .values(params)
    ;
}

export async function insertItemImpression(params) {
    params.viewed_at = sql.raw("NOW() at time zone 'utc'");
    return db
        .insert(tables.item_impressions)
        .values(params)
    ;
}

export async function insertShare(params) {
    params.shared_at = sql.raw("NOW() at time zone 'utc'");
    return db
        .insert(tables.shares)
        .values(params)
    ;
}

export async function getPromptTexts(agentId, params, limit) {

    const conditions = [eq(tables.prompts.agent_id, agentId)];
    for (const field in params) {
        conditions.push(eq(tables.prompts[field], params[field]));
    }

    const prompts = await db
        .select({
            prompt: tables.prompts.prompt,
        })
        .from(tables.prompts)
        .where(
            and(...conditions)
        )
        .orderBy(desc(tables.prompts.prompted_at))
        .limit(limit)
    ;

    const texts = prompts.map((row) => row.prompt);

    return texts;

}

export async function getIntegrationById(id) {
    const rows = await db
        .select({
            ...getTableColumns(tables.agent_integrations)
        })
        .from(tables.agent_integrations)
        .where(and(
            eq(tables.agent_integrations.id, id),
            eq(tables.agent_integrations.is_active, true)
        ))
        .limit(1)
    ;
    return (rows.length > 0) ? rows[0] : null;
}

export async function getNumPromptsByIntegrationConversationId(
    agentId,
    integrationId,
    conversationId
) {
    const rows = await db
        .select({ count: sql`count(*)`.mapWith(Number) })
        .from(tables.prompts)
        .where(
            and(
                eq(tables.prompts.agent_id, agentId),
                eq(tables.prompts.integration_id, integrationId),
                eq(tables.prompts.conversation_id, conversationId),
            )
        )
    ;
    return rows[0].count;
}

export async function insertIntegrationMessage(params) {
    params.received_at = sql.raw("NOW() at time zone 'utc'");
    const records = await db
        .insert(tables.agent_integration_messages)
        .values(params)
        .returning({ id: tables.agent_integration_messages.id })
    ;
    // debug(records);
    return records[0];
}

export async function getRecentIntegrationMessages(integrationId, conversationId, limit) {
    const messages = await db
        .select()
        .from(tables.agent_integration_messages)
        .where(
            and(
                eq(tables.agent_integration_messages.integration_id, integrationId),
                eq(tables.agent_integration_messages.conversation_id, conversationId)
            ),
        )
        .orderBy(desc(tables.agent_integration_messages.received_at))
        .limit(limit + 1) // Include most recent prompt
    ;
    return messages;
}

export async function getPromptById(id, agentId) {
    const rows = await db
        .select()
        .from(tables.prompts)
        .where(
            and(
                eq(tables.prompts.id, id),
                eq(tables.prompts.agent_id, agentId),
            )
        )
        .limit(1)
    ;
    return (rows.length > 0) ? rows[0] : null;
}

export async function getCtas(agentId, client, integrationId = null) {
    const rows = await db
        .select({
            id: tables.agent_ctas.id,
            display_mode: tables.agent_ctas.display_mode,
            content: tables.agent_ctas.content,
            timing_mode: tables.agent_ctas.timing_mode,
            response_number: tables.agent_ctas.response_number,
            response_basis: tables.agent_ctas.response_basis,
        })
        .from(tables.agent_ctas)
        .where(getCtaConditions(agentId, client, integrationId))
        .orderBy(asc(tables.agent_ctas.priority))
    ;
    return rows;
}

export async function getCtaCount(agentId, client, integrationId) {
    return db.$count(tables.agent_ctas, getCtaConditions(agentId, client, integrationId));
}

function getCtaConditions(agentId, client, integrationId) {
    return and(
        eq(tables.agent_ctas.agent_id, agentId),
        tables.agent_ctas.is_active,
        or(
            ['standalone', 'embedded', 'api'].includes(client) ? tables.agent_ctas[`${client}_active`] : false,
            and(
                eq(client, 'integration'),
                integrationId ? sql.raw(`"agent_ctas"."integrations" @> '[${integrationId}]'`) : false,
            )
        )
    );
}

export async function getResponseCounts(agentId, conversationId, sessionId, deviceId, userId) {
    const rows = await db
        .select({
            conversation: db.$count(tables.prompts, eq(tables.prompts.conversation_id, conversationId)),
            session: db.$count(tables.prompts, eq(tables.prompts.session_id, sessionId)),
            device: db.$count(tables.prompts, eq(tables.prompts.device_id, deviceId)),
            user: db.$count(tables.prompts, eq(tables.prompts.user_id, userId)),
        })
        .from(tables.prompts)
        .where(eq(tables.prompts.agent_id, agentId))
        .limit(1)
    ;
    return rows[0];
}

export async function insertSearch(params) {
    params.searched_at = sql.raw("NOW() at time zone 'utc'");
    const records = await db
        .insert(tables.searches)
        .values(params)
        .returning({ id: tables.searches.id })
    ;
    // debug(records);
    return records[0];
}

export async function updateSearchResults(searchId, results = [], cached = false) {
    return db
        .update(tables.searches)
        .set({ results: results, cached: cached })
        .where(eq(tables.searches.id, searchId))
    ;
}
