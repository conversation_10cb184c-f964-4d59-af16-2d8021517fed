import {
    getAgentByHost,
    agentIsInactive,
    getAgentTokenFromHeader,
    getAgentTokenFromQuery,
} from './agent';
import {
    debug,
} from './helpers';
import {
    getPayloadValidation,
    requestIsSameOrigin,
} from './request';
import {
    get503Response,
    get403Response,
    get422Response,
} from './response';
import {
    searchCorpus,
    prependMetaFilterCombinator,
    filterUniqueDocs,
} from './vectara';
import { insertSearch, updateSearchResults } from './db';
import { waitUntil } from '@vercel/functions';
import { getCachedValue, setCachedValue, getCacheTtl } from './cache';

let shouldLog = false;

const API_KEY_HEADER = 'x-api-key';
const API_KEY_PARAM = 'api_key';

// Payload schema validator -- had to precompile b/c the compiler won't run on edge runtime
const validatePayload = require('../api/v1/search/validation');

// Handle the chat completion
export async function handleSearch(payload, req) {

    const startTime = performance.now();

    const validation = await getPayloadValidation(payload, validatePayload);
    if (validation) {
        return get422Response(validation);
    }

    // Get Agent by hostname; bail if there's no match
    const AGENT = await getAgentByHost(req.headers.get('host'));
    if (agentIsInactive(AGENT) || !AGENT.has_semantic_search) {
        return get503Response();
    }

    // Bail if this is an API request and the API token is invalid
    const sameOrigin = requestIsSameOrigin(req.headers);
    const tokenId = await getAgentTokenFromHeader(AGENT.id, req.headers, API_KEY_HEADER) ||
        await getAgentTokenFromQuery(AGENT.id, req.nextUrl.searchParams, API_KEY_PARAM)
    ;
    if (!sameOrigin && !tokenId) {
        return get403Response();
    }

    const headers = Object.fromEntries(await req.headers.entries());
    const query = Object.fromEntries(req.nextUrl.searchParams.entries());
    shouldLog = (payload.debug || query.debug || headers['x-debug'] || AGENT.debug);
    const cacheTtl = getCacheTtl(headers, query);
    debug(shouldLog, 'SEARCH PAYLOAD', payload);
    debug(shouldLog, 'CACHE TTL', cacheTtl);

    // Default the model to 'source'
    if (!payload.filters) {
        payload.filters = {};
    }
    if (!payload.filters.model) {
        payload.filters.model = 'source';
    }

    // Default the limit to ''
    if (!payload.limit) {
        payload.limit = 10;
    }

    const searchId = await getSearchId(
        payload.query,
        payload.filters,
        AGENT,
        tokenId,
        payload.prompt_id ?? null,
    );

    let results = null;
    let cached = false;
    if (cacheTtl) {
        results = await getCachedValue(payload, cacheTtl, AGENT.id, 'search');
        if (results) {
            cached = true;
            debug(shouldLog, 'CACHE RESULTS', results);
        }
    }

    if (!results) {
        results = await searchCorpus(
            process.env.SEARCH_CORPUS_KEY,
            process.env.SEARCH_API_KEY,
            AGENT,
            payload.query,
            getMetaFilter(payload.filters),
            parseInt(process.env.SEARCH_NUM_RESULTS) * 3,
            process.env.SEARCH_SCORE_THRESHOLD,
            process.env.SEARCH_LEXICAL_INTERPOLATION
        );
        debug(shouldLog, 'SEARCH RESULTS', results);
        if (cacheTtl) {
            await setCachedValue(payload, results, cacheTtl, AGENT.id, 'search');
        }
    }

    if (results) {

        const documents = filterUniqueDocs(
            results.map((x) => expandSearchDocumentAttrs(x.document_id, x.document_metadata))
                // .filter((media) => shouldIncludeMedia(media, AGENT))
            ,
            parseInt(process.env.SEARCH_NUM_RESULTS)
        );
        // debug(documents);

        const endTime = performance.now();
        const startTs = new Date(performance.timeOrigin + startTime);
        const endTs = new Date(performance.timeOrigin + endTime);
        const responseParams = {
            results: documents,
            timings: {
                start: startTs.toISOString(),
                end: endTs.toISOString(),
                elapsed: endTime - startTime,
            },
        };

        const searchResults = documents.map((x) => {
            return {
                id: x.id,
                model: x.model,
            };
        });
        debug(shouldLog, 'MAPPED RESULTS', searchResults);
        waitUntil(updateSearchResults(searchId, searchResults, cached));

        // Set the response options
        const responseOptions = {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
            },
        };

        return new Response(
            JSON.stringify(responseParams),
            responseOptions
        );

    }

    return get422Response();

}

// Get the meta filter used for search
function getMetaFilter(filters) {

    const singleValues = ['types', 'languages', 'classification_ids', 'user_ids', 'team_ids'];
    let metaFilter = '';
    for (let param in filters) {

        if (Array.isArray(filters[param]) && !singleValues.includes(param)) {

            let subfilter = '';
            filters[param].forEach(id => {
                if (typeof(id) === 'string') {
                    id = `'${id}'`;
                }
                subfilter += prependMetaFilterCombinator(`(${id} IN doc.${param})`, subfilter, 'OR');
            });
            if (subfilter.length > 0) {
                metaFilter += prependMetaFilterCombinator(`(${subfilter})`, metaFilter, 'AND');
            }

        } else {

            let operator = '=';
            let value = `'${filters[param]}'`;

            if (Array.isArray(filters[param])) {
                if (param === 'ids') {
                    filters[param] = filters[param].map((x) => `'${filters.model}.${x}'`);
                } else if (typeof(filters[param][0]) === 'string') {
                    filters[param] = filters[param].map((x) => `'${x}'`);
                }
                value = `(${filters[param].join(',')})`;
                operator = 'IN';
            } else if (typeof(filters[param]) == 'number') {
                value = filters[param];
            }

            if (param.substring(0, 3) === 'min') {
                operator = '>=';
            } else if (param.substring(0, 3) === 'max') {
                operator = '<=';
            }

            if (singleValues.includes(param)) {
                param = param.substring(0, param.length - 1);
            } else if (['min', 'max'].includes(param.substring(0, 3))) {
                param = param.substring(4);
            }

            metaFilter += prependMetaFilterCombinator(`(doc.${param} ${operator} ${value})`, metaFilter, 'AND');

        }

    }

    debug(shouldLog, 'RAG FILTER', metaFilter);

    return metaFilter;

}

// Expand RAG document meta data for source
export function expandSearchDocumentAttrs(id, meta) {
    // Only include attributes that are needed; discard the rest
    let item = meta;
    item.id = parseInt(id.substring(item.model.length+1));
    return item;
}

// Insert the new search log and return the search ID
async function getSearchId(
    query,
    filters,
    agent,
    tokenId = null,
    promptId = null
) {
    const record = await insertSearch({
        query: query,
        filters: filters,
        prompt_id: promptId,
        agent_id: agent.id,
        agent_token_id: tokenId,
    });
    return record.id;
}
