import { debug } from './helpers';

export function updatePrompt(promptId, endpoint, params) {
    params.prompt_id = promptId;
    async function postData() {
        return await fetch(
            endpoint,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify(params),
            }
        );
    }
    return postData();
}

// Validate a payload
export async function getPayloadValidation(payload, validationFunc) {

    const validPayload = validationFunc(payload);
    if (!validPayload) {
        // debug(validationFunc.errors);
        let responsePayload = {
            success: false,
            errors: [],
        };
        validationFunc.errors.forEach(function(error) {
            if (error.keyword === 'required') {
                responsePayload.errors.push(`Input ${error.message}.`);
            } else if ((error.instancePath.length === 0) && error.params.additionalProperty) {
                responsePayload.errors.push(`'${error.params.additionalProperty}' is not a valid parameter.`);
            } else if (error.keyword !== 'oneOf') {
                responsePayload.errors.push(`The '${error.instancePath.substring(1)}' parameter ${error.message}.`);
            }
        });
        return responsePayload;
    }

    return false;

}

// Whether or not the request came from the same place as it's being processed
export function requestIsSameOrigin(headers) {
    const headerOrigin = headers.get('origin');
    if (headerOrigin) {
        const originHost = new URL(headerOrigin).host;
        debug(true, 'ORIGIN HOST', originHost);
        return (originHost === headers.get('host'));
    }
    debug(true, 'TARGET HOST', headers.get('host'));
    return false;
}

export function redirectToUrl(path, params, key = null, value = null) {
    if (key && value) {
        params.set(key, value);
    }
    let redirectUrl = path;
    if (params.toString().length > 0) {
        redirectUrl += '?' + params.toString();
    }
    window.location.href = redirectUrl;
}
