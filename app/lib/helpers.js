import util from 'util';

const debugOptions = { showHidden: false, depth: null, maxArrayLength: null, maxStringLength: null, colors: (process.env.NEXT_PUBLIC_ENV === 'local') };
export function debug(shouldLog, type, obj) {
    const globalLog = envBoolean(process.env.NEXT_PUBLIC_DEBUG);
    if (globalLog || shouldLog) {
        if (typeof(obj) !== 'undefined') {
            console.log(`${type}:`, util.inspect(obj, debugOptions));
        } else {
            console.log(type);
        }
    }
}

export function replaceTokens(string, replacements) {
    for (const key in replacements) {
        string = string.replaceAll('{' + key + '}', replacements[key]);
    }
    return string;
}

export function envBoolean(envVar) {
    return (envVar.trim() === 'true');
}

export function getMediaUrl(path) {
    return process.env.NEXT_PUBLIC_MEDIA_URL + path;
}

export function dedupeArray(arr, key) {
    return Array.from(new Map(arr.map(obj => [obj[key], obj])).values());
}

export function shouldIncludeSource(source, agent) {
    return (
        (!agent.disable_community_corpus && source.is_approved && (source.weight >= parseInt(process.env.NEXT_PUBLIC_RAG_MIN_WEIGHT))) ||
        ((agent.sources?.length > 0) && agent.sources.includes(parseInt(source.id))) ||
        ((agent.categories?.length > 0) && agent.categories.some(element => source.categories.includes(element))) ||
        ((agent.tags?.length > 0) && agent.tags.some(element => source.tags.includes(element))) ||
        ((agent.collections?.length > 0) && agent.collections.some(element => source.collections.includes(element))) ||
        ((agent.contributors?.length > 0) && agent.contributors.some(element => source.contributors.includes(element))) ||
        (agent.classification_id && (agent.classification_id === source.classification)) ||
        (agent.use_team_corpus && (agent.team_id === source.team))
    );
}

export function shouldIncludeMedia(media, agent) {
    return (
        (!agent.disable_community_corpus && media.is_approved) ||
        ((agent.media_collections?.length > 0) && agent.media_collections.some(element => media.collection_ids.includes(element))) ||
        (agent.use_team_corpus && (agent.team_id === media.team_id))
    );
}

export function getShareUrl(agentId, conversationId, promptId) {
    const raw = replaceTokens(
        process.env.NEXT_PUBLIC_SHARE_TOKEN,
        {
            agentId: agentId,
            conversationId: conversationId,
            promptId: promptId,
        }
    );
    const token = btoa(raw);
    const host = (typeof window !== 'undefined') ? window.location.protocol + '//' + window.location.host + window.location.pathname : null;
    return host + replaceTokens(process.env.NEXT_PUBLIC_SHARE_URL, { token: token});
}

export function getConversationId() {
    return getLocalStorageId('conversation-id');
}

export function deleteConversationId() {
    return deleteLocalStorage('conversation-id');
}

export function getDeviceId() {
    return getLocalStorageId('device-id');
}

function getLocalStorageId(key) {
    if ((typeof window !== 'undefined') && localStorage) {
        let id = localStorage.getItem(key);
        if (!id) {
            id = crypto.randomUUID();
            localStorage.setItem(key, id);
        }
        return id;
    } else {
        return null;
    }
}

function deleteLocalStorage(key) {
    if ((typeof window !== 'undefined') && localStorage) {
        localStorage.removeItem(key);
    }
}

export function getDbTranslation(translations, lng) {
    return translations ? translations[lng] || translations[process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE] : null;
}

export function agentIsInactive(agent) {
    return (!agent || !agent.is_active || !agent.team_is_active);
}

export function getSupportedLanguages(agent, allLanguages) {
    let languages = Object.keys(JSON.parse(process.env.NEXT_PUBLIC_LANGUAGES));
    if (agent?.supported_languages?.length > 0) {
        languages = agent.supported_languages;
    } else if (agent?.model_languages?.length > 0) {
        languages = agent.model_languages;
    } else if (agent?.auto_translate) {
        languages = allLanguages;
    }
    return languages;
}

export function shouldUseRealTimeTranslation(
    currentLanguage,
    defaultLanguage,
    autoTranslate = false,
    autoTranslateLanguages = []
) {
    return (
        autoTranslate &&
        (currentLanguage !== defaultLanguage) &&
        (
            !autoTranslateLanguages ||
            (autoTranslateLanguages.length === 0) ||
            autoTranslateLanguages.includes(currentLanguage)
        )
    );
}

export function parseFontFromUrl(url) {
    try {
        const urlObject = new URL(url);
        const params = new URLSearchParams(urlObject.search);
        const familyParam = params.get('family');
        if (familyParam) {
            const fontName = familyParam.split(':')[0].replace(/\+/g, ' ');
            if (fontName) {
                return fontName;
            }
        }
    } catch (error) {
        console.error('Error parsing font from URL:', error);
    }
    return null;
}

export function getTheme(queryTheme, agentTheme) {
    return stringToNull(queryTheme) || agentTheme || 'dark';
}

export function getColor(queryColor, agentColor) {
    return stringToNull(queryColor) || agentColor || '#7137ff';
}

function stringToNull(str) {
    return (!str || (str === 'null')) ? null : str;
}

export function getMessageText(message) {
    let partsProp = 'parts';
    if (message.content) {
        if (typeof(message.content) === 'string') {
            return message.content.trim();
        }
        partsProp = 'content';
    }
    let messageText = '';
    message[partsProp].filter((part) => part.type === 'text').forEach((part) => {
        messageText += part.text;
    });
    return messageText.trim();
}

export function camelToSnake(str) {
    if (!str) return null;
    return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
}

export function getMessageCta(ctas, responseCounts, lng, stripHtml = false) {
    for (const cta of ctas) {
        const responseNum = responseCounts ? responseCounts[cta.response_basis.replace('_id', '')] || 0 : 0;
        if (
            (responseNum > 0) &&
            (
                ((cta.timing_mode === 'once') && (responseNum === cta.response_number)) ||
                ((cta.timing_mode === 'interval') && (responseNum % cta.response_number === 0)) ||
                ((cta.timing_mode === 'threshold') && (responseNum >= cta.response_number)) ||
                (cta.timing_mode === 'always')
            )
        ) {
            cta.content = getDbTranslation(cta.content, lng);
            if (stripHtml) {
                cta.content = cta.content.replace(/<[^>]+>/g, '');
            }
            return cta;
        }
    }
    return null;
}
