import { cookies } from 'next/headers';

async function getSessionValueFromCookie(key) {
    const cookieStore = await cookies();
    return cookieStore.get(key)?.value;
}

async function setSessionValue(key, value) {
    const cookieStore = await cookies();
    cookieStore.set(key, value);
}

export async function getSessionValue(key) {
    const value = await getSessionValueFromCookie(key);
    if (!value) {
        const newValue = crypto.randomUUID();
        await setSessionValue(key, newValue);
        return newValue;
    }
    return value;
}

export async function deleteSessionValue(key) {
    const cookieStore = await cookies();
    cookieStore.delete(key);
}
