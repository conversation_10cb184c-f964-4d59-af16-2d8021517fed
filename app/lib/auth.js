export async function getApiToken() {
    const res = await fetch(
        process.env.NEXT_PUBLIC_API_URL + '/token',
        {
            credentials: 'include',
            cache: 'no-store',
            headers: {
                Accept: 'application/json',
            }
        }
    );
    const json = await res.json();
    return json.token;
};

export async function getApiUser(token) {
    const res = await fetch(
        process.env.NEXT_PUBLIC_API_URL + '/user',
        {
            cache: 'no-store',
            headers: {
                Authorization: 'Bearer ' + token,
                Accept: 'application/json',
            }
        }
    );
    return await res.json();
}

export function redirectToLogin(currentUrl) {
    window.location = process.env.NEXT_PUBLIC_IDENTITY_URL + '/login?redirect&redirect_url=' + encodeURI(window.location.href);
    // if (typeof window !== 'undefined') {
    //     window.location = process.env.NEXT_PUBLIC_IDENTITY_URL + '/login?redirect&redirect_url=' + encodeURI(window.location.href);
    // } else {
    //     redirect(process.env.NEXT_PUBLIC_IDENTITY_URL + '/login?redirect&redirect_url=' + encodeURI(currentUrl));
    // }
}
