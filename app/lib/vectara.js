import {
    replaceTokens,
    debug
} from './helpers';
import {
    getCachedValue,
    setCachedValue,
} from './cache';

export async function callCorpusEndpoint(
    corpusId,
    apiKey,
    method,
    apiEndpoint,
    data
) {

    apiEndpoint = replaceTokens(apiEndpoint, {corpusId: corpusId});

    // If this is a GET request, add data as query string
    // debug(data);
    if (method === 'GET') {
        apiEndpoint += '?' + serialize(data);
    }

    const params = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'customer-id': process.env.VECTARA_CUSTOMER_ID,
            'x-api-key': apiKey,
        },
        signal: AbortSignal.timeout(parseInt(process.env.VECTARA_TIMEOUT))
    };

    // If this is a POST request, add data as JSON
    if (method === 'POST') {
        params.body = JSON.stringify(data);
    }

    try {
        const req = await fetch(apiEndpoint, params);
        return await req.json()
    } catch (e) {
        debug(true, 'VECTARA TIMEOUT');
        debug(true, 'ERROR',e);
        return null;
    }

}

// Prepend meta filter combinator
export function prependMetaFilterCombinator(clauses, existing, combinator = 'AND') {
    return (((clauses.length > 0) && (existing.replace('(', '').replace(')', '').trim().length > 0)) ? ` ${combinator} ` : '') + clauses;
}

export async function searchCorpus(
    corpusId,
    apiKey,
    agent,
    query,
    metaFilter,
    limit,
    scoreThreshold = null,
    lexicalInterpolation = null,
    cacheTtl = null
) {

    let results = [];

    if (!scoreThreshold) {
        scoreThreshold = process.env.VECTARA_SCORE_THRESHOLD;
    }
    if (!lexicalInterpolation) {
        lexicalInterpolation = process.env.VECTARA_LEXICAL_INTERPOLATION;
    }

    // Create API request
    const qry = {
        query: query,
        search: {
            metadata_filter: metaFilter,
            context_configuration: {
                sentences_before: parseInt(process.env.VECTARA_CONTEXT_SENTENCES),
                sentences_after: parseInt(process.env.VECTARA_CONTEXT_SENTENCES),
            },
            lexical_interpolation: parseFloat(lexicalInterpolation),
            limit: limit,
            reranker: {
                type: 'customer_reranker',
                reranker_name: 'Rerank_Multilingual_v1',
                cutoff: parseFloat(scoreThreshold),
            },
        },
    };

    if (cacheTtl) {
        const cachedValue = await getCachedValue(qry, cacheTtl, agent.id, 'rag');
        if (cachedValue) {
            results = cachedValue;
        }
    }

    if (!results.length) {
        const response = await callCorpusEndpoint(corpusId, apiKey, 'POST', process.env.VECTARA_API_ENDPOINT_SEARCH, qry);
        // debug(res);
        // debug(res.search_results);
        if (response && response.search_results) {
            if (cacheTtl) {
                await setCachedValue(qry, response.search_results, cacheTtl, agent.id, 'rag');
            }
            results = response.search_results;
        } else {
            debug(true, 'SEARCH RESPONSE', JSON.stringify(response, 4));
            return null;
        }
    }

    return results;

}

export function formatDocumentIds(ids) {
    return ids.filter(x => x).map(x => "'" + x + "'").join(',');
}

export function serialize(data) {
    return new URLSearchParams(data).toString();
}

export function filterUniqueDocs(results, limit) {
    let docs = [];
    let ids = [];
    for (const doc of results) {
        if (!ids.includes(doc.id)) {
            docs.push(doc);
            ids.push(doc.id);
            if (docs.length >= limit) {
                break;
            }
        }
    }
    return docs;
}
