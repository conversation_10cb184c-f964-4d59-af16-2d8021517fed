import { replaceTokens } from './helpers';

export function sendBeacon(endpoint, params) {
    return navigator.sendBeacon(
        endpoint,
        JSON.stringify(params)
    );
}

export function trackView(sourceId, promptId, userId) {
    return sendBeacon(
        '/api/view',
        {
            source_id: sourceId,
            prompt_id: promptId,
            user_id: userId,
        }
    );
}

export function trackImpression(sourceId, promptId, userId) {
    return sendBeacon(
        '/api/impression',
        {
            source_id: sourceId,
            prompt_id: promptId,
            user_id: userId,
        }
    );
}

export function trackShare(promptId, userId, agentId) {
    return sendBeacon(
        '/api/share',
        {
            shared_prompt_id: promptId,
            user_id: userId,
            agent_id: agentId
        }
    );
}

export function trackCta(promptId, agentId, ctaId) {
    return sendBeacon(
        '/api/cta',
        {
            agent_id: agentId,
            prompt_id: promptId,
            cta_id: ctaId,
        }
    );
}

export function getReferralUrl(itemModel, itemId, promptId, url, userId = null) {

    const REFERRAL_ENDPOINT = '/api/referral?prompt_id={promptId}&item_model={itemModel}&item_id={itemId}&url={url}';

    let referralUrl = replaceTokens(
        REFERRAL_ENDPOINT,
        {
            promptId: promptId,
            itemModel: itemModel,
            itemId: itemId,
            url: encodeURIComponent(url),
        }
    );
    if (userId) {
        referralUrl += '&user_id=' + userId;
    }

    return referralUrl;

}

export function getSourceReferralUrl(source, promptId, userId) {

    let url = source.referral_url ?? source.url;
    if (!url) {
        let keywords = '"' + source.title + '" by ' + (source.authors ?? source.contributor_names);
        url = process.env.NEXT_PUBLIC_AFFILIATE_SEARCH_URL.replace('{keywords}', keywords);
    }

    return getReferralUrl(
        'source',
        source.id,
        promptId,
        url,
        userId
    );

}
