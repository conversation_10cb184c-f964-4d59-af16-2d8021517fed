import { NextResponse } from "next/server";

// OK
export function get200Response(content = 'OK') {
    return getResponse(200, content);
}

// Forbidden
export function get403Response(content = 'Forbidden') {
    return getResponse(403, content);
}

// Request Timeout
export function get408Response(content = 'Request Timeout') {
    return getResponse(408, content);
}

// Invalid payload
export function get422Response(payload) {
    return getResponse(422, payload);
}

// Server Error
export function get500Response(content = 'Server Error') {
    return getResponse(500, content);
}

// Service Unavailable
export function get503Response(content = 'Service Unavailable') {
    return getResponse(503, content);
}

function getResponse(code, content) {
    const payload = !content ?
        null :
        (
            (typeof content === 'string') ?
                (code + ': ' + content) :
                JSON.stringify(content, null, 2)
        )
    ;
    return new NextResponse(payload, { status: code });
}
